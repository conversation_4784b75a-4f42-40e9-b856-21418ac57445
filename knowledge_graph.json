{"nodes": [{"id": "cb8a4a9b-968d-436b-8fdf-120cb8240548", "properties": {"page_content": "layout: default title: Advisory Group Members description: \"This page lists the members of the Diversity, Inclusion & Belonging Advisory Group.\"\n\n{{< group-by-expertise \"Diversity, Inclusion & Belonging Advisory Group\" >}}", "document_metadata": {"source": "Sample_Docs_Markdown\\advisory-group-members.md"}}, "type": "document"}, {"id": "2bf75794-91b1-478a-bd94-495c589f3f35", "properties": {"page_content": "title: \"The Ally Lab\" description: Learn what is an ally, how to be an ally and what it means to be an ally.\n\nWhat is an ally?\n\nA diversity, inclusion and belonging \"ally\" is someone who is willing to take action in support of another person, in order to remove barriers that impede that person from contributing their skills and talents in the workplace or community.\n\nBeing an ally is a verb, this means that you proactively and purposefully take action and is not something forced upon you.\n\nHow to be an ally\n\nIt is not required to be an ally to work at GitLab. At GitLab it is required to be inclusive.\n\nBeing an ally goes a step beyond being inclusive to taking action to support marginalized groups. The first step in being an ally is self-educating.\n\nThis ally lab will provide you with some of the tools, resources and learning activities to help you grow as an ally.\n\nSkills and Behaviors of allies\n\nTo be an effective ally it is important to understand some of the skills and behaviors great allies exhibit.\n\nActive listening\n\nNeutral and nonjudgmental.\n\nPatient (periods of silence are not \"filled\")\n\nVerbal and nonverbal feedback to show signs of listening (e.g., smiling, eye contact, leaning in, mirroring)\n\nAsking questions.\n\nReflecting back what is said.\n\nAsking for clarification.\n\nSummarizing.\n\nEmpathy & Emotional Intelligence\n\nAn example of this could be: A colleague comes to you and tells you that their pronouns are being misused often at work and it is making them feel uncomfortable and they are avoiding social calls and interactions. Whilst you haven’t experienced this yourself and unlikely you would experience this, you allow yourself to think of situations where you have felt uncomfortable at work before. You also put yourself consciously into the shoes of your colleague and think of a way you can practically help. You offer to your colleague that in the next 5 calls they participate in you will be on the call and actively point out misuse of their pronouns to other colleagues to take away some of the emotional burden.\n\nActive learning about other experiences\n\nYou go beyond performative actions for example black squares on Instagram for Black Lives Matter, but actively does the work to understand the pain, struggle and experience of those burdened.\n\nThis could look like: You are managing black team members, an incident has occurred externally that could affect the mental health of those team members. You actively research the experience and historical context of the trauma associated with the incident. You use this to ensure you are informed and able to appropriately apply empathy if a team member approaches you to ask for assistance.\n\nHumility\n\nNon-defensive\n\nWillingness to take on feedback\n\nYou aren’t going to get it right all the time and you have to be ok with that. Be willing to take feedback on and not let it deter you from continuing to be an ally.\n\nExample of this could be: You are in a safe space with an underrepresented group acting as an ally and absorbing information. A point comes up that you are passionate about and you talk over someone in the group and take over the conversation. After the meeting someone from the group jumps on a Zoom meeting with you and explains that it felt you took away the viewpoints of a number of people from the URG because you took over the conversation and interrupted an individual. You apologize, take on the feedback, ask for any tips on how to make sure it doesn’t happen again and take the necessary steps. --- One of the mistakes that often happens here is being defensive or justifying the action. The group will already know you are operating with good intent but generally are wanting to help you level up in their lived experience.\n\nCourage\n\nComfortable getting uncomfortable\n\nSpeak up where others don't or can't\n\nThe empathy example is also a good example of this.\n\nSelf-awareness\n\nOwn and use your privilege\n\nThis could look like: You are in a product meeting and the meeting will be making critical decisions about the product roadmap for the next three months and you notice that everyone in the meeting is of the same gender and race. You use your privileged situation in the meeting to point this out and ask the people in the meeting. Who should be invited to ensure we are getting a diverse perspective and viewpoint on the agenda items for the meeting?\n\nAction orientated\n\nYou see something, you say something\n\nThe example above is a good example of this: Ensure decisions and conversations have diverse voices. I.E. you are in a meeting and everyone looks the same, insist on other perspectives.\n\nIn a group setting when a discussion or comment is verbalized that could be controversial use language similar to this to course correct the conversation:\n\n\"I would like us all to be aware of our language and/or acknowledgements and ensure we are being respectful to all individuals. Thank you.\"\n\n\"I am practicing being an ally and as a reminder I would like to ensure we are all using inclusive language\"\n\nWhat it means to be an ally\n\nTake on the struggle as your own\n\nStand up, even when you feel uncomfortable\n\nTransfer the benefits of your privilege to those who lack it\n\nAcknowledge that while you, too, feel pain, the conversation is not about you\n\nConcepts & Terms\n\nPrivilege: an unearned advantage given to some people but not all\n\nOppression: systemic inequality present throughout society that benefits people with more privilege and is a disadvantage to those with fewer privileges\n\nAlly: a member of a social group that has some privilege, that is working to end oppression and understands their own privilege\n\nPower: The ability to control circumstances or access to resources and/or privileges\n\nMarginalized groups: a person or group that are treated as unimportant, insignificant or of lower status. In a workplace setting, employees could be treated as invisible, as if they aren't there or their skills or talents are unwelcome or unnecessary\n\nPerformative allyship: referring to allyship that is done to increase a person's social capital rather than because of a person's devotion to a cause. For example some people used #metoo during the Me Too movement, without actually bringing more awareness or trying to effect change.\n\nTips on being an ally\n\nIdentifying your power and privilege helps you act as an ally more effectively\n\nFollow and support those as much as possible from marginalized groups\n\nWhen you make a mistake, apologize, correct yourself, and move on\n\nAllies spend time educating themselves and use the knowledge to help\n\nAllies take the time to think and analyze the situation\n\nAllies try to understand Perception vs. Reality\n\nAllies don’t stop with their power they also leverage others powers of authority\n\nSee our Ally Resources Page for more resources on being an ally.\n\nAllyship & Empathy\n\nBeing an Ally Requires Empathy\n\n{{< youtube \"1Evwgu369Jw\" >}}\n\nResponding with Empathy\n\nWhat to say, when you don’t know what to say:\n\nAcknowledgement of their pain. “I’m sorry you are going through this.” “That must be hard.”\n\nShare how you feel. “Wow. I don’t know what to say.” “It makes me really sad to hear this happened.”\n\nShow Gratitude that the person opened up. “Thank you for sharing with me.” “This must be hard to talk about. Thanks for opening up to me.”\n\nShow Interest. “How are you feeling about everything?” “Is there anything else you want to share?”\n\nBe Encouraging. “You are brave / strong / talented.” “You matter.”\n\nBe Supportive. “I’m here for you.” “I’m happy to listen any time.”\n\nBoot and Sandal Methophor\n\nImagine you are wearing a heavy boot (represents privilege) and you are stepping on someone’s foot that is only wearing sandals (represents oppression). If someone says, “Ouch, you are stepping on my toes!” How do you react?\n\nProblems with common responses to mistakes become obvious:\n\nCentering the mistake around yourself: “I can’t believe you think I’m a toe-stepper! I’m a good person!”\n\nDenial that others’ experiences are different from your own: “I don’t mind when people step on my toes.”\n\nDerailing: “Some people don’t even have toes, why aren’t we talking about them instead?”\n\nRefusal to center the impacted: “All toes matter!”\n\nTone policing: “I’d move my foot if you’d ask me more nicely.”\n\nDenial that the problem is fixable: “Toes getting stepped on is a fact of life. You’ll be better off when you accept that.”\n\nVictim blaming: “You shouldn’t have been walking around people with boots!”\n\nWithdrawing: “I thought you wanted my help, but I guess not. I’ll just go home.”\n\nInstead, you would respond with the following:\n\nCenter the impacted: “Are you okay?”\n\nListen to their response and learn.\n\nApologize for the impact, even though you didn’t intend it: “I’m sorry!”\n\nStop the instance: move your foot\n\nStop the pattern: be careful where you step in the future. When it comes to oppression, we want to actually change the “footwear” to get rid of privilege and oppression (sneakers for all!), but metaphors can only stretch so far!\n\nImprove allyship skills by following the three C's\n\nConsciousness\n\nCourage\n\nCompassion\n\nHow diversity shows up on teams\n\nImage\n\nAreas you can show allyship\n\nRecruiting & Hiring\n\nSourcing\n\nInterviewing\n\nCompensation\n\nGuidance & Support\n\nHelping through challenges\n\nListening and empathizing\n\nProviding perspective\n\nMentorship\n\nDifficult Conversations\n\nPerformance conversations\n\nOngoing feedback\n\nGrowth & Career Development\n\nCareer planning\n\nSkill development\n\nMentorship and sponsorship\n\nExamples of Allyship at GitLab\n\nTBC\n\nAlly Lab Learning Group\n\n{{< youtube \"dPywm-j1iic\" >}}\n\nThe Ally Lab Learning Group is an initiative to learn to become or be a better ally within a collaborative group of peers who are seeking the same aim of allyship growth. All team members have the ability to be an ally whether you are a part of an underrepresented group (URG) or not, there are URGs that may not belong to and you have the ability to be an ally to them. There will be regular intakes of team members into groups.\n\nThe next intake: You can sign up here\n\nTo sign up: TBC\n\nThe Group:\n\nALLG Facilitator\n\n5-10 team members\n\nDiversity within the group where self-identification is available\n\nWhat you will need:\n\nThe ability to have 1 synchronous 30-45min a week (Timezone convenient location)\n\nA higher intent to become an ally\n\nWhat you will do:\n\nSession 1: Async ally training and courses. Synchronous meeting to discuss what you have learned.\n\nEquality Ally Strategies\n\nCultivate Equality at Work\n\nCommunicating with Empathy\n\nEffective Listening\n\nBuilding Trust\n\nThe importance of trust\n\nHow to engage meaningfully in allyship and anti-racism - OPTIONAL\n\nInclusive mindset of committed allies - OPTIONAL\n\nSession 2: The importance of being an ally and why you want to be an ally.\n\nSession 3: Work as a group to discuss a number of scenarios and how to tackle them as an ally.\n\nSession 4: Together, write a commitment to allyship and the values you will abide by to be an ally.\n\nIn the last session you will decide as a group a short commitment statement that should be shared on the Diversity, Inclusion & Belonging Sharing Page. Think about what you have individually and collectively learnt during the experience.\n\nCreate 2-5 values that, as a group, you will hold in your continuous learning in allyship and take into account when situations arise.\n\nOnce completed you now have a safe group to discuss allyship with, either to get advice, hold yourself accountable to or run through a situation. Things you can do post the 4 week ALLG:\n\nSchedule a quarterly or bi-yearly call with your group\n\nAsk members of your group for regular coffee chats to discuss the above\n\nReflect regularly on your commitment and values you agreed to and situations where you could have displayed allyship\n\nAlly Training\n\nWe held a 50 minute Live Learning Ally Training on 2020-01-28. The recording follows along with the slide deck and agenda.\n\n{{< youtube \"wwZeFjDc4zE\" >}}\n\nExternal Ally Training\n\nThere are some essential skills that are required to be an ally, here are a number of trainings that will help you enhance your allyship skills. Some of these are not allyship specific but will sharpen your skills in those important areas.\n\nEquality Ally Strategies\n\nChampion Workplace Equality\n\nEffective Listening\n\nHow to engage meaningfully in Allyship\n\nBecoming a true ally\n\nBuilding Trust\n\nWhy trust matters\n\nAlly Learning Activity and Scenarios\n\nGitLab Diversity, Inclusion & Belonging resources\n\nAllies familiarize themselves with GitLab's general DIB content\n\nDiversity, Inclusion & Belonging page\n\nGender and Sexual Orientation Identity Definitions and FAQ\n\nDIB training resources\n\nUnconscious bias\n\nAlly Resources\n\nHere are additional resources on being an ally\n\nGuide to allyship\n\n5 Tips For Being An Ally\n\nAlly skills workshop. Check out the materials section with a handout PDF (linking to many more resources), slides PDF, videos, and more.\n\nWhy cisgender allies should put pronouns on their name tag", "document_metadata": {"source": "Sample_Docs_Markdown\\being-an-ally.md"}, "headlines": ["What is an ally?", "How to be an ally", "Skills and Behaviors of allies", "What it means to be an ally", "Concepts & Terms"], "summary": "An ally is someone who supports marginalized groups by taking action to remove barriers in the workplace or community. Being an ally involves active listening, empathy, self-education, humility, courage, and leveraging privilege to support others. It requires understanding concepts like privilege, oppression, and performative allyship. The Ally Lab at GitLab provides resources and learning activities to help individuals grow as allies, including training sessions, group discussions, and scenarios to practice allyship skills. Key aspects of allyship include acknowledging mistakes, apologizing, and continuously improving through consciousness, courage, and compassion.", "summary_embedding": [0.04218335, -0.13946269, 0.15429276, -0.09883382, -0.00396135, -0.04655963, 0.0315976, 0.03547234, 0.01858649, -0.00019661, 0.09197208, 0.04131348, 0.04498795, -0.0266851, 0.14582802, 0.07904388, -0.14911711, 0.00927048, 0.01778493, -0.00628285, -0.04393595, -0.05252332, -0.11212067, 0.0622795, 0.00022298, -0.03821852, 0.02491879, -0.02528739, -0.01572308, -0.07381647, 0.1686738, 0.01839446, -0.01680431, 0.02300861, 0.0468279, 0.08318203, -0.02426047, 0.04613211, 0.01968901, 0.07794388, 0.01101744, -0.01893214, -0.04232151, 0.02432648, 0.08048441, 0.02716061, -0.00787681, 0.0150527, 0.01248585, -0.10870036, 0.14896259, 0.02672349, -0.01146619, 0.04202145, -0.02568758, -0.05049824, -0.03159005, -0.02097492, -0.00229569, -0.03219828, -0.02820419, 0.03429994, -0.10228256, -0.01141239, -0.01996624, 0.07031178, -0.01460725, -0.05259999, -0.04329983, 0.05145566, 0.05284469, 0.01794641, -0.05322997, 0.09820601, 0.0130218, 0.03325507, 0.02760958, 0.03485033, 0.01093763, -0.04494417, 0.05882635, 0.03862848, 0.01533308, -0.08347207, 0.04089859, 0.00367242, -0.03651913, 0.0085746, -0.02295049, -0.00042466, 0.07017092, 0.02044638, 0.05099827, 0.02148668, 0.00643656, -0.0237933, 0.03752745, -0.02784916, 0.05834582, 0.00451012, 0.00026228, -0.01468069, -0.03545717, -0.01806373, -0.01331123, 0.00515079, 0.03694696, -0.01536182, 0.04098023, -0.05444767, -0.11071318, 0.08347016, 0.05450759, -0.00544579, -0.04019924, 0.08624998, 0.01941972, 0.04472265, -0.11919601, -0.02267937, 0.03242209, 0.04763633, -0.06239363, -0.01118107, -0.06271593, 0.00014292, 0.00944675, 0.00827852, -0.02791326, 0.00821729, -0.03452352, 0.00523167, -0.00542541, -0.06140894, 0.00244628, 0.01498005, -0.02393318, -0.03068744, 0.02692982, -0.01214576, -0.0365244, 0.02613361, -0.01244614, 0.03421772, -0.02295532, -0.05707815, 0.02365046, -0.04272174, -0.00823882, 0.07439746, 0.05930774, 0.03558691, -0.08032431, -0.03696142, -0.03327578, 0.05892262, 0.04673586, -0.01444886, 0.03187463, 0.01955745, 0.00796737, 0.04706607, 0.01672473, -0.00245426, -0.01139348, -0.0666265, 0.07078164, 0.00934337, 0.00952103, 0.01156143, 0.03078026, 0.0715975, 0.03583173, -0.02439013, 0.00565658, -0.01387365, -0.03216663, -0.00519077, 0.02062073, -0.00387964, 0.02356315, -0.03429528, 0.04175977, -0.01778174, 0.00834051, 0.01325833, 0.05560343, -0.02134508, -0.00053991, 0.07718173, -0.00635869, 0.0185329, -0.0023092, 0.02893513, -0.02020792, 0.02288542, -0.0261016, -0.0321311, -0.00506086, -0.04106388, 0.08070301, -0.03532201, 0.03461111, -0.00071844, -0.01279742, 0.02127689, 0.01043475, 0.03293178, 0.01217517, 0.02486427, 0.02754946, 0.05717128, -0.01496504, -0.02487314, -0.04833102, 0.04171168, -0.02069853, 0.04094739, 0.05588578, -0.0147139, -0.00623055, -0.02193907, 0.01955841, -0.01550824, -0.0001146, -0.06923912, -0.01497817, 0.02605125, 0.00269532, -0.03467775, -0.04379909, -0.04335078, 0.02151778, 0.03927004, 0.03205182, -0.03964984, -0.04527521, 0.01650665, 0.02997763, -0.01437443, -0.03482417, -0.01595503, -0.04849136, -0.02630623, -0.04705621, 0.01469621, -0.04570967, 0.02272468, -0.00219061, 0.02510533, 0.01685514, -0.04835991, -0.01925308, -0.00899814, 0.00478015, -0.01993068, 0.02904625, -0.01656031, -0.06752386, 0.01698068, -0.01065822, -0.01020307, 0.00939106, -0.00087489, 0.0349001, -0.02840739, 0.01230364, -0.02542787, -0.01633697, -0.05612535, -0.00567764, 0.01455803, -0.06776788, -0.01204675, -0.00047831, 0.00629657, -0.00757037, -0.02632464, 0.04176207, -0.01654003, -0.00820585, -0.00653909, -0.0010308, -0.00333915, -0.03393752, 0.0537364, 0.02803242, -0.00385868, -0.00247599, 0.00771509, 0.04138676, -0.01003894, -0.01055871, -0.0241711, -0.03711476, 0.02361531, -0.05034075, -0.00404282, 0.0216671, 0.04710158, -0.01933284, 0.00867379, 0.05171912, -0.08817416, 0.0032285, -0.02485673, 0.00318061, -0.05585987, -0.04207578, 0.00670215, -0.00723029, -0.02289103, 0.01423828, 0.00362067, 0.02033488, -0.03500072, -0.03280063, -0.0122183, -0.02343024, -0.01798273, 0.00071564, 0.02071046, -0.01561692, -0.00751912, 0.02498111, -0.01755446, -0.00208227, -0.03858886, 0.0174038, 0.01527233, 0.00862938, 0.0097287, 0.03933986, 0.01585696, -0.04346218, -0.03365427, -0.00490045, -0.06666459, -0.01548866, 0.07432529, -0.00813247, -0.01795719, 0.01350351, -0.00451528, -0.01579077, 0.00797848, 0.01580842, -0.00125697, 0.00855086, -0.01270455, -0.0376198, -0.01684087, -0.05349875, -0.02691652, -0.00075395, 0.0471094, 0.01424414, 0.03327598, -0.01138091, -0.02420126, 0.02773883, 0.01489597, -0.00295964, 0.030279, -0.0060714, -0.04102669, 0.04510597, 0.03940389, -0.02782928, 0.055758, 0.00415064, -0.00769016, -0.0491472, 0.00508588, -0.031725, 0.03472855, 0.01612423, 0.04850508, 0.00968298, 0.01117916, 0.03249529, 0.02302343, -0.00585211, -0.0096307, -0.00366959, 0.02063381, 0.04693763, -0.01527918, -0.01038743, -0.01745349, -0.00723975, 0.02036287, -0.02467753, 0.006603, 0.00632381, -0.03016423, -0.0406431, -0.02835899, -0.02592502, 0.02226182, 0.00414718, 0.02156896, -0.00838734, 0.01671035, -0.00306061, 0.02886201, 0.00259697, -0.01741003, -0.04063448, 0.01164815, -0.00074105, 0.01554843, -0.03224299, 0.00158744, -0.01864686, -0.05268404, 0.01314092, 0.05291389, 0.04064985, 0.03091625, -0.01155904, -0.02087682, 0.01814571, -0.03773819, 0.01718473, 0.01620025, -0.00728892, -0.02841244, 0.00174348, 0.02120383, -0.02443645, 0.01935624, -0.03586559, -0.00975146, -0.02880599, 0.01799783, 0.00734413, -0.01328761, -0.01221165, 0.00664488, -0.02271329, 0.01108631, -0.00192573, -0.01895803, -0.05413746, -0.0034717, 0.00229679, 0.00611289, -0.02062172, -0.01694511, -0.02387868, 0.03772048, 0.07104513, -0.00447226, -0.01018764, -0.01005998, -0.00700173, -0.01292402, -0.02822932, -0.03340088, 0.04442845, -0.00428904, 0.01824371, -0.01505473, 0.02284395, -0.00832009, 0.04142308, 0.0347181, 0.01306863, 0.02627601, 0.03597192, -0.02991991, -0.03019066, -0.01082918, 0.00104978, 0.00119719, -0.02233968, 0.03700691, -0.06302578, -0.0482769, 0.03650375, -0.00289874, 0.03161921, 0.01362056, -0.03652854, -0.03025687, -0.02626197, 0.01482754, -0.01292122, 0.00082113, -0.03001837, 0.02026898, -0.02838868, -0.00774741, 0.00089415, 0.00649877, -0.04370497, 0.00720327, 0.01878621, -0.0084317, -0.00299426, 0.01000709, -0.00644331, -0.02970515, -0.00023558, -0.03642657, 0.00146546, 0.0120241, 0.02159821, 0.06989154, -0.02505654, -0.06299599, 0.01120988, -0.00231788, -0.0081197, 0.03358684, 0.02360669, 0.02309869, 0.0206183, 0.01120767, -0.00753141, 0.03712797, 0.02035184, 0.0088285, 0.0246764, 0.00019393, 0.0261617, -0.04669095, -0.03317978, 0.00683846, 0.00201093, -0.01666403, 0.01335459, -0.01849936, 0.0133342, 0.02105743, -0.02142097, -0.05359271, -0.06181917, 0.03451486, -0.04126772, -0.01479585, 0.00432161, -0.02798723, -0.00402626, 0.00566357, -0.00851843, 0.00191024, 0.03766836, 0.03511258, -0.00573686, 0.01200416, 0.01669966, 0.02929918, -0.00565997, -0.04245247, -0.00101131, 0.01535594, -0.01080675, 0.01568038, 0.02923082, -0.00236566, 0.00500282, -0.04309157, -0.01847021, -0.04453522, -0.00518248, 0.01021485, -0.04380256, 0.00838661, -0.03218181, 0.0124008, -0.00070871, 0.01522511, -0.04842566, 0.03284955, -0.00753464, -0.01046425, 0.05046781, -0.01922723, -0.03935413, -0.02946122, 0.01594147, -0.05026834, 0.01156792, 0.00726192, 0.01805519, -0.01442458, 0.02748171, -0.03238611, -0.00086717, 0.00773818, 0.00324015, 0.03711725, 0.01034549, 0.02788852, -0.03575731, -0.02037389, -0.02460859, -0.00053946, -0.00723583, 0.01726029, -0.0499858, 0.02359121, -0.0020106, 0.01474998, -0.01181641, 0.01944105, 0.00894113, -0.04346764, 0.02730462, 0.00162165, -0.00742213, 0.00120365, 0.01948911, -0.00087351, 0.01895759, -0.02578443, -0.02605875, 0.02574377, 0.0125661, 0.00726633, 0.0320643, 0.02599936, -0.00533089, 0.00410182, 0.01301109, 0.06147746, -0.03213998, 0.0014423, -0.01116415, 0.03203312, 0.02106674, -0.02970847, 0.02933701, -0.04171717, 0.01732109, -0.04816601, 0.0024904, -0.01100094, -0.00730886, 0.05273279, 0.01387767, 0.00516143, -0.0225065, -0.02502153, 0.04937709, -0.00456777, 0.027704, -0.02311047, -0.01448046, 0.02669826, 0.00551507, 0.02565184, 0.00162475, -0.01416124, -0.0146781, 0.04294817, -0.01318735, -0.00585019, 0.01944885, -0.01554784, 0.01927006, -0.00721632, -0.02922665, -0.0059357, -0.05859021, 0.00768986, -0.00262737, -0.02250645, -0.02782273, -0.00612633, -0.00056483, 0.03302886, 0.02975841, -0.02857716, 0.00302145, -0.01254482, -0.02370027, 0.03284571, -0.00324225, -0.03388659, -0.01842114, 0.02158435, -0.02167064, -0.01478285, -0.01381214, 0.02294226, 0.02493904, -0.00439739, -0.01348727, 0.00926616, 0.02547385, -0.04912025, 0.01095539, -0.00411012, -0.00841504, -0.00091086, -0.02894074, 0.0036704, -0.01441331, 0.00260041, 0.02065076, 0.0237855, 0.00161808, 0.01249804, 0.03158951, -0.01761089, 0.02018017, 0.03305358, -0.02223458, 0.0273968, -0.00065223, -0.00679536, 0.00436067, -0.01024302, 0.03909226, 0.0232719, -0.00999174, 0.02097916, 0.04080373, -0.01799208, 0.01849909, -0.01815416, -0.02866387, 5.764e-05, 0.02871452, 0.00353473, 0.0460346, 0.03488424, -0.01487795, -0.0023954, -0.03672157, 0.01097179, 0.00482815, -0.0111192, -0.00346185, 0.01707959, 0.00050398, 0.00367635, -0.00636267, 0.00417974, 0.00037759, 0.05508003, 0.00852435, 0.02932315, -0.02818425, 0.00534695, 0.03893987, 0.03360879, 0.00246264, 0.02349748, -0.01024702, -0.03646972, 0.01763257, -0.01947795, 0.02335384, 0.02970927, 0.02556474, 0.00493156, -0.0141403, 0.04058543, -0.00545665, 0.01358032, 0.00722257, -0.06087442, -0.00644518, -0.01193915, -0.01561683, 0.00551045, -0.00974704, -0.01317049, 0.01854355, -0.00237991, 0.0060171, 0.01364882, -0.03008467, -0.00119938, -0.00223196, 0.01592827, -0.02046287, 0.00195415, 0.01300396, 0.03624114, -0.00374867, -0.02393456, 0.02649041, -0.03259294, -0.00964454, -0.03241559, -0.02889502, -0.04478889, 0.01096172, -0.00209522, 0.01115857, 0.02136734, -0.013292, -0.01096046, 0.00641221, 0.01537103, -0.00801138, -0.02517099, 0.00018728, -0.01037968, 0.00789196, 0.0309425, 0.00185539, 0.01595742, -0.00014775, -0.00052363, 0.03060815, -0.03632448, 0.00488558, -0.02363913, -0.02963847, 0.01028048, -0.02184451, -0.0091082, 0.01281249, 0.04652061, 0.02452321, 0.01555043, 0.01547146, 0.01007278, 0.01754688, 0.04706331, 0.00842764, -0.01600117, 0.00896272, -0.00939176, 0.02416606, -0.00537564, 0.01408689, 0.03892218, 0.01513823, -0.02144133, 0.02726408, -0.00171931, 0.00764482, 0.02729863, 0.02043058, 0.00725242, -0.00669851, -0.00402502, -0.0021489, -0.02238951, -0.0035755, 0.02708903, -0.00893833, 0.00172512, 0.00073876, 0.02061776, 0.00407248, 0.03555315, -0.00625253, 0.00560061, -0.0426112, -0.02209126, 0.00036137, -2.76e-05, 0.00224876, -0.01153112, -0.03509402, -0.02664623, 0.00705923, 0.02081922, -0.03553853, 0.01177639, -0.030867, -0.00939098, -0.01443832, 0.01302979, 0.00472689, 0.02608914, 0.00274436, -0.03547327, -0.02062306, -0.00383858, -0.00328946, 0.0089246, -0.02982962, -0.00053668, -0.01573786, -0.04032656, -0.00796805, 0.03604788, 0.02671176, -0.04869925, 0.01535185, 0.03102091, 0.0237732, -0.02012696, 0.00133847, -0.04875369, -0.00454311, -0.02384065, 0.00184562, 0.0064417, 0.01062828, -0.00559989, -0.03030436, -0.00501555, -0.00554861, 0.00355414, -0.02207095, 0.00759729, -0.01018372, -0.03896519, 0.01697017, 0.0084287, 0.01070068, -0.04180843, 0.0152115, 0.05015313, -0.00446603, -0.01159515, 0.02216419, 0.04745199, 0.00890071, -0.02784497, 0.03356297, 0.00918933, 0.00249381, 0.01003855, -0.01035142, -0.02723977, -0.03386096, 0.01799758, -0.00440168, -0.01885587, 0.01019151, 0.03120194, -0.00188408, -0.00602535, 0.02016865, -0.0077796, -0.00676586, -0.02939251, 0.00488652, -0.014043, -0.03668854, 0.03055345, -0.01205484, 0.02222251, -0.00531828, 0.01014201, 0.02014874, -0.02619443, -0.01239338, -0.01291071, -0.05029568, 0.02819626, 0.0166302, -0.02787947, 0.00296611, 0.01847457, -0.00754127, 0.02554371, -0.03526009, -0.00347433, 0.0421253, -0.01754732, 0.03074857, 0.01855417, -0.00088397, 0.00317699, -0.00176542, -0.00833319, -0.02280463, 0.01382932, -0.00342068, -0.02176668, 0.02936203, -0.0466711, 0.02645827, 0.03653665, -0.03783192, -0.01449851, 0.02386008, -0.00430753, -0.00892687, 2.64e-06, 0.02409637, -0.00913451, -0.01651382, -0.01978569, -0.03289649, 0.00992986, 0.0026603, 0.00674189, 0.02554428, 0.00137758, -0.03946745, -0.00775044, -0.0120708, -0.01416135, -0.01042817, -0.02952798, -0.0340552, -0.0239172, 0.00051389, -0.00160326, 0.012031, 0.00877748, 0.01502994, 0.01381518, 0.01674239, -0.00966401, -0.00371187, 0.01033748, -0.00762875, 0.00341106, -0.01486308, 0.00222185, 0.0183349, 0.03952335, 0.07110982, 0.01336603, 0.0155472, -0.00781896, -0.03166978, -0.01190956, -0.02233861, 0.00360043]}, "type": "document"}, {"id": "56dc7a77-29c1-4a1c-9dc4-c52dc4a8757a", "properties": {"page_content": "title: \"Being Inclusive\" description: \"We are all responsible for creating and maintaining an inclusive environment at GitLab.\"\n\nWhat is fundamental for a successful inclusive environment\n\nWe are all responsible for creating and maintaining an inclusive environment at GitLab.\n\nWe all need to feel empowered to contribute and collaborate.\n\nDiversity, Inclusion & Belonging at GitLab\n\nDiversity includes all the differences we each have, whether it be where we grew up, where we went to school, experiences, how we react, age, race, gender, national origin, things we can and can not see, the list goes on.\n\nInclusion is understanding or recognizing all these differences and inviting someone to be a part of things and collaborate, etc.\n\nBelonging is when you feel your insights and contributions are valued. It goes back to team members feeling they can bring their full selves to work.\n\nSee the Diversity, Inclusion, and Belonging page page for more information.\n\nWhat inclusive behaviors look like\n\nInclude and seek input from team members across a wide variety of backgrounds.\n\nActive listening - listen carefully to the person speaking and playback what they have said in an effort to show understanding.\n\nMake a habit of asking questions.\n\nIf you have a strong reaction to someone, ask yourself why. Look inward.\n\nAddress misunderstandings and quickly resolve disagreements.\n\nMake a point to understand each team member's contribution efforts and leverage them as much as possible.\n\nEnsure all voices are heard. Include everyone as much as possible in discussions.\n\nAssume positive intent and examine your assumptions/judgements.\n\nSee our values page for futher information on inclusive language.\n\nInclusion Training\n\nIn December 2019, we held 3 sessions on Inclusion Training. Below you can find a recorded session that follows along with the slide deck and agenda.\n\n{{< youtube \"gsQ2OsmgqVM\" >}}\n\nInclusion Knowledge Assessment\n\nAnyone can earn a GitLab Inclusion Certification. To obtain certification, you will need to complete the Inclusion Knowledge Assessment Quiz and earn at least an 80%. Once the quiz has been passed, you will receive an email with your certification that you can share on your personal LinkedIn or Twitter pages. If you have questions, please reach out to our L&D <NAME_EMAIL>.", "document_metadata": {"source": "Sample_Docs_Markdown\\being-inclusive.md"}}, "type": "document"}, {"id": "fc144afa-006a-45bb-aa05-ff5c7d035dc6", "properties": {"page_content": "title: \"Building an Inclusive Remote Culture\" description: \"We are passionate about all remote working and enabling an inclusive work environment.\"\n\nWe are passionate about all remote working and enabling an inclusive work environment. There isn't one big activity we can take to accomplish this. Instead, it is a mix of numerous activities and behaviors combined to enable our team members to feel they belong in GitLab.\n\nThose activities and behaviors include:\n\nDIB Events\n\nDIB Advisory Group\n\nDiversity, Inclusion & Belonging TMRGs - Team Member Resource Groups\n\nOur Values\n\nFamily and Friends first, Work second\n\nInclusive language and pronouns\n\nParental leave\n\nAsynchronous communication and workflows\n\nTips for Companies\n\nDefining Diversity, Inclusion & Belonging\n\nA great place to begin is to set a foundation of the basic understanding of how your company defines these terms. An example is GitLab's Diversity, Inclusion & Belonging value, supported by an evolving list of operating principles.\n\nIn other places, you may hear them used interchangeably. Understanding their differences is essential to driving the initiatives. As an example, you may hire many diverse candidates, but if you don't create an inclusive environment the work can be in vain.\n\nEvaluating the company's current DIB landscape\n\nConsider what you are already doing in this space.\n\nWhat is the current feedback?\n\nWhat are team members saying with company engagement surveys?\n\nWhat are the goals you are wanting to achieve?\n\nWhat are the metrics saying?\n\nDiversity, Inclusion & Belonging survey\n\nA company survey is a great way to get a sense of team members' thoughts and concerns. The DIB team at GitLab runs an annual survey via CultureAmp to gauge where we are as a company in terms of diversity, inclusion & belonging. We take the results and implement projects and solutions based on insights from the survey results.\n\nNaming this body of work\n\nAlthough Diversity, Inclusion & Belonging are often understood globally, there are other terms that can be leveraged to name your efforts. The naming should be unique to your company. Examples could include Belonging, Inclusion & Collaborations, etc.\n\nDeveloping a mission statement\n\nWhen creating a diverse and inclusive culture, most companies will develop a mission statement to support their vision. Your mission statement should articulate the purpose of your strategy. In a few sentences, you should be able to succinctly provide the why and the how. Be sure to take into account your company’s current overall mission and vision. It is best to align your DIB mission and vision with your organization’s overarching mission and vision. To do this, you may consider how your DIB Strategy can build on, scale, or enhance the organization’s mission and vision.\n\nCreating TMRGs\n\nIn general, TMRGs are an excellent support system and key to providing awareness, respect, and building diversity, inclusion & belonging within the workplace. These groups are a proven way to increase cultural competency, retention of team members, provide marketplace insights to the business, attract diverse talent, and more. The endorsement of TMRGs allows team members to identify common interests and decide how they can be shared with others. When creating TMRGs there are a few initial steps to consider:\n\nCreating guidelines to help support TMRGs being stood up within your company\n\nNaming of each TMRG\n\nRoles within each TMRG\n\nAligning to company strategy\n\nCreating forms, Google groups, ways of tracking attendance for TMRG events, and membership metrics\n\nCreating a Diversity, Inclusion & Belonging Advisory Group\n\nConsider creating this group as your highest level of global voices: a team of company influencers who can be instrumental in driving DIB efforts from a global perspective. How do you do this? GitLab conducted a global \"All Call\" for those who would be interested in joining and advised them to provide \"why\" DIB was important to them, along with other questions such as division, location, etc. When we were reviewing we were able to have the best possible outcome of representation across the globe. Additional support in sustaining the group would be:\n\nDIB Advisory group guidelines\n\nAppointing an Executive sponsor from the company\n\nDesignating leads of the group\n\nDeciding on when it is time to enact or rotate the opportunity for new advisory group members\n\nMore to come\n\nDIB Initiatives\n\nDIB Awards for Recognition\n\nDIB Surveys\n\nDIB Framework Inclusive Benefits\n\nTips for Managers\n\nSet aside time to show up for planned DIB events\n\nYou may be surprised by how much seeing your face in these events validates this as a worthwhile use of time which is valued by the company. Seeing you in attendance also opens the door to future conversations that might begin with: \"I saw you in XYZ meeting, what did you think of such-and-such topic?\"\n\nIncorporate DIB into your team meetings\n\nConnecting to team members is key to understand who they are and what they bring to the team. A great initial step is to start with open discussions. You could start by opening your next team meeting to chat about what they feel inclusion looks like, what is important to them as a team to feel included, etc. This could then move into monthly or quarterly team DIB icebreakers, trainings, etc. The idea is to make sure DIB is not touched on once and never mentioned again, but more of an understood aspect of your team environment. You can do this by setting and communicating DIB goals for your team, and sharing how we will measure success (an idea could be sharing DIB survey data with team members).\n\nEncourage Diversity of Thought and Speaking Up\n\nDon’t assume you know more than others. Give people a chance to add their perspective or expertise. Teach people how to disagree, set the expectation that it is OK to disagree, and encourage people to do it day-to-day. Celebrate diverse thoughts and thank people for making contributions, particularly if they disagree with you. It takes courage to disagree publicly with a leader.\n\nConnect with your team members\n\nTo help team members feel comfortable being themselves, leaders should consider authentic ways to connect to their team members. Everyone wants to feel visible and included in order to perform their best work. Being visible usually includes being seen for accomplishments, acknowledgment of contributions, what is the same, and what differs from team member to team member. Leaders and all team members should show an interest in and respect for differences and contributions.\n\nConsiderations could be that of language/terminology such as “spouses” or “partners” instead of making assumptions about team members sexual orientation. Being considerate of dietary restrictions when choosing food options for Contribute or other local gatherings. Acknowledging birthdays, recalling personal things that might have been mentioned in past calls such as moving to a new home, an ill family member or team member, and following up authentically.\n\nFor more information see our Inclusive Language.\n\nAsk employees what pronouns they use\n\nPronouns are a large piece of a person's identity and are often used to communicate a person’s gender, which is why it is so important to get it right. Asking for a person's pronouns and using those pronouns consistently shows that you respect their identity, but it also helps to create a more welcoming, safe, and supportive environment where people can feel comfortable being themselves. This is a change that goes a long way to foster inclusion.\n\nBe mindful of the times of your meetings\n\nIt's important to consider global times and alternating meetings to accommodate regions. Be mindful of families, commitments, observances, holidays, and meeting times that may be out of team members' working hours. Every meeting time won't be perfect for everyone (which is why all meetings have an agenda), but making a conscious effort to alternate times is to ensure the same people aren't being excluded. For more, view our Inclusive Meetings operating principle.\n\nBe a Role Model\n\nAs a manager, you are in a unique position to model good DIB practices for the rest of your team. Be authentic, own up to mistakes, and commit to doing better next time. Don’t dwell on it. Set a good example by admitting that you make mistakes and invite people to help you and each other by speaking up in accordance with our feedback guidelines. By holding each other accountable, we get better as a team.\n\nTips for Team Members\n\nSchedule a Coffee Chat with a Team Member\n\nUnderstanding GitLab is fully remote, there is an opportunity to get to know team members beyond your geographical location as well as outside of your division. This provides an opportunity to:\n\nlearn more about other cultures and work divisions\n\ncultivate better global communication\n\nmake progress toward building an inclusive environment\n\nCoffee chats are suggested during onboarding but you don't need to stop there. It is recommended to continue this action whenever you would like to. Please take a look at our GitLab team page and feel free to select someone for a coffee chat!\n\nHold Each Other Accountable\n\nHold each other accountable and speak up when someone uses non-inclusive language. We should also celebrate when we are inclusive, and strive to support each other every day.\n\nAvoid Identity Assumption Statements\n\nIdentity is a personal thing. Looks or names can imply diverse attributes, but they are not perfect identity indicators. Avoid making statements about someone's perceived race, gender, age, ethnicity, or other personal characteristics. When people are typecast or feel misunderstood, they are less likely to feel that they belong.\n\nTip to Uncover Unconscious Bias\n\nWhen you are dealing with a specific situation, you can mentally flip the situation around and see how that feels. For example, flip it from a woman to a man, and if it feels off then you might have a bias. By putting ourselves in someone else's shoes, we can better understand their point of view and be more supportive.\n\nMore to come\n\nConsider joining a TMRG (Team Member Resource Group)\n\nSupport others as an Ally\n\nReview the Diversity, Inclusion & Belonging page", "document_metadata": {"source": "Sample_Docs_Markdown\\building-diversity-and-inclusion.md"}, "headlines": ["DIB Events", "Tips for Companies", "Creating TMRGs", "Tips for Managers", "Tips for Team Members"], "summary": "Building an inclusive remote culture involves a mix of activities and behaviors to make team members feel they belong. This includes DIB events, advisory groups, TMRGs, and promoting inclusive language and practices. Companies should evaluate their current DIB landscape through surveys and align DIB missions with overall company goals. Managers can support DIB by attending events, encouraging diversity of thought, and being role models. Team members are encouraged to hold each other accountable, avoid identity assumptions, and join TMRGs. Creating an inclusive environment requires global consideration, such as meeting times and using correct pronouns, to ensure all team members feel valued and respected.", "summary_embedding": [0.********, -0.********, 0.0460735, -0.********, 0.0299515, -0.********, -0.********, 0.0330657, 0.********, -0.********, 0.********, 0.********, 0.********, -0.********, 0.********, -0.********, -0.********, 0.********, -0.********, 0.********, 0.********, -0.********, -0.********, 0.********, -0.********, -0.********, 0.********, -0.********, 0.********, -0.********, 0.********, -0.********, 0.********, 0.0439539, 0.138207, 0.********, 0.********, 0.********, 0.********, 0.********, -0.********, 0.********, 0.********, -0.********, 0.********, -0.********, -0.********, 0.********, 0.********, 0.********, -0.********, 0.********, 0.********, 0.********, 0.********, 0.********, -0.********, -0.0414583, -0.********, -0.********, 0.********, 0.03801786, -0.10525735, 0.02262086, 0.02849548, 0.10938606, 0.03135781, 0.01460552, -0.03034651, -0.06843457, -0.07099275, -0.02263963, -0.0419494, 0.06746625, 0.02420005, 0.02893843, 0.00188848, 0.01137778, -0.02230799, 0.01070723, -0.04673975, -0.00245712, 0.02263458, -0.03992416, 0.0349022, -0.01913072, -0.09175464, -0.01243803, -0.00457064, 0.06036334, 0.05068346, -0.02504734, -0.05617565, -0.01763323, -0.0085085, 0.00021303, 0.07513154, -0.07106028, -0.01394169, -0.0984926, 0.07571131, -0.01928973, -0.00531385, -0.03967756, -0.04574848, 0.02193134, 0.05565794, 0.03176451, 0.02863899, -0.04446321, 0.02291615, 0.09242381, 0.01302957, 0.01257679, -0.02007151, -0.04411427, -0.00374297, -0.04568126, 0.06927244, -0.02045229, -0.00287259, 0.04413207, -0.0659614, 0.06610703, 0.04146344, -0.09364662, -0.00532569, 0.05962449, -0.03629491, -0.04239594, -0.04057243, -0.02085176, 0.04701174, -0.01529872, 0.05965636, -0.03487364, 0.0237468, -0.07517646, -0.0025386, -0.01597755, -0.03228187, -0.00234765, -0.06022211, 0.07394739, -0.02147194, 0.0222828, -0.0220343, -0.0200712, 0.01518612, 0.02794668, -0.01082031, 0.04670412, 0.00439, 0.01332489, -0.00497406, -0.04243556, 0.02667899, 0.03160061, 0.08429144, 0.06502064, 0.05882342, 0.04444977, 0.01756725, 0.06285474, 0.05005431, -0.05229066, -0.01891832, 0.03845753, -0.00354415, -0.03417656, 0.00043127, 0.05591101, -0.00021438, 0.00279462, 0.03314904, -0.00431441, -0.02312226, 0.06830315, 0.00915014, 0.01003684, -0.01350701, 0.00483788, -0.0456579, 0.04467209, -0.01116574, -0.01932625, -0.01880324, -0.00469413, -0.0165993, 0.03332786, -0.02026912, -0.01876988, -0.00478505, -0.02750291, -0.01826099, 0.0284604, -0.02303074, -0.03837254, 0.00853261, -0.02984196, 0.02358139, -0.02180115, 0.03411483, -0.02784054, -0.01414536, 0.02289153, -0.0256802, -0.02925977, -0.01774597, 0.03421149, -0.04586603, 0.03634211, 0.0078229, -0.02307991, 0.01141716, -0.00556404, 0.05251567, -0.00233894, -0.0107612, -0.08712101, -0.02829703, -0.03185948, -0.04476602, 0.02843104, -0.0236659, -0.020394, -0.04985536, 0.02130364, 0.02541805, 0.0789204, -0.02890562, -0.02506873, -0.02720002, -0.00736487, 0.00664005, -0.05466526, 0.07558294, -0.02830984, -0.00989539, 0.03459173, 0.00663395, -0.045252, -0.01901491, -0.04432863, -0.00317147, 0.00304951, 0.01032056, 0.04632239, -0.04697424, -0.01332556, -0.05004997, -0.08167258, 0.01017365, -0.06214475, -0.01061881, -0.0271898, 0.01149955, -0.03531018, -0.03129293, 0.04897574, 0.0011269, -0.02617585, 0.01442322, 0.01568208, 0.04488244, 0.01069002, -0.05129856, 0.02937036, -0.02099329, 0.02050806, -0.04603588, -0.00211205, -0.0113187, -0.0277746, 0.04153669, 0.04069334, 0.01708868, -0.05367728, 0.04898038, 0.01897993, -0.0437819, -0.00070466, -0.02944162, -0.00230379, -0.00584897, -0.0035388, 0.01258603, 0.0130573, -0.03230999, 0.01631573, 0.01651136, 0.02388222, -0.01071029, 0.03182792, 0.00896512, 0.01713388, -0.0372433, -0.00432122, -0.00805836, -0.01929347, 0.01050905, -0.0040741, 0.03068603, 0.01782984, 0.00725958, -0.05723758, -0.03855592, -0.00477182, -0.00942566, 0.00713149, -0.00503424, 0.07774318, 0.01051533, -0.00388492, 0.01468146, 0.02464361, 0.03025667, -0.01548969, -0.01127739, 0.03550731, 0.01557117, 0.01100334, -0.00789254, -0.00386973, 0.00451715, 0.00930722, -0.04259991, -0.04364715, -0.03078496, 0.02533364, -0.033655, 0.02982517, 0.01447381, 0.04859035, -0.02979429, -0.0446185, -0.02471245, -0.01690014, 0.00840946, 0.03380716, -0.00681378, -0.01875949, -0.00535678, -0.00114782, -0.01470639, -0.04909733, 0.01572184, 0.00382216, 0.04622595, -0.00945948, -0.00484915, 0.00855261, -0.01952277, -0.03024068, -0.00091785, 0.03879543, -0.04850127, 0.00870018, 0.01988245, -0.02795958, -0.00415057, 0.00657169, 0.01715852, 0.03482381, -0.00248361, -0.00681632, 0.03406079, 0.03070862, 0.00622497, 0.03131356, -0.02542456, 0.00337932, -0.01839054, 0.05153236, 0.01552845, -0.00172928, 0.03536602, -0.01359158, 0.01862024, 0.00982581, -0.01366904, -0.00231407, 0.00516233, 0.00875994, 0.02844612, 0.03065521, 0.05139126, -0.00792381, 0.05827798, -0.02507252, 0.03103526, 0.00545028, -0.03712219, -0.02004242, -0.03296504, -0.02545052, -0.02696112, -0.01919386, 0.02004443, 0.05560496, 0.04846152, 0.0022291, -0.03667114, -0.02660107, 0.02226932, -0.00399008, -0.01011631, -0.02594417, -0.04357092, 0.00899455, 0.05423426, 0.00799032, -0.01494273, 0.00981512, -0.00832972, 0.02894412, 0.01783226, 0.03885644, 0.00465747, -0.00058452, -0.01150315, -0.06131499, 0.03598276, -0.02066916, 0.01640143, -0.03854926, -0.03535246, -0.03183618, 0.05334378, 2.914e-05, 0.00887706, 0.01764033, -0.0173384, -0.05980092, -0.0204551, 0.03619801, -0.01015006, 0.00898651, -0.00714983, -0.02680738, 0.05145424, 0.01135129, 0.0118938, -0.0414209, -0.01165916, 0.01922053, 0.01309288, -0.01909665, 0.00817283, -0.00592081, -0.0126745, 0.04304215, 0.01326993, 0.00114753, 0.00862329, 0.02304202, -0.0267835, 0.01155092, -0.00546291, 0.04675341, -0.00800368, 0.00675559, 0.0257117, 0.01097624, 0.02947044, 0.02550063, -0.01235621, -0.01513321, -0.02674775, 0.00320379, 0.00815754, -0.02972607, -0.02385964, 0.04051786, -0.00414029, 0.08624732, 0.00127374, 0.02147039, -0.03168742, -0.02578843, 0.02313346, -0.005366, -0.04406617, 0.04050827, -0.02916644, -0.06047975, 0.01078478, 0.01185818, -0.04069467, -0.00564494, -0.00529101, 0.01100165, 0.02399796, 0.02738495, -0.03280253, 0.04451936, 0.0144197, -0.03905045, -0.01493867, -0.00195987, -0.0263641, 0.05427548, -0.03209061, 0.01182062, -0.01129759, -0.04760453, 0.02921594, 0.01692587, -0.02676324, 0.01975285, -0.05867729, -0.03723673, 0.00516573, -0.04310163, 0.02745331, -0.03637204, -0.02021382, 0.02026495, 0.0043158, -0.01866602, -0.03386155, -0.01634767, -0.00078283, 0.01246303, 0.02192893, -0.00074964, -0.01546161, 0.02224346, -0.03730776, 0.03304473, -0.0094343, 0.02776816, 0.00814535, 0.00799642, -0.00976446, 0.02136144, -0.00639934, -0.01935021, -0.03187236, 0.03690362, -0.0189149, 0.00132481, -0.00267063, -0.03903536, 0.01871306, 0.02188076, -0.01240019, -0.02066727, 0.03920957, 0.05459303, -0.05630475, 0.01921389, 0.00491961, 0.02271813, 0.02468674, 0.01026062, -0.02749017, -0.01184032, 0.02216584, 0.00179907, 0.04001723, 0.00283476, 0.00230715, -0.05761803, 0.02667342, 0.02974915, 0.03031243, 0.02748534, -0.01616925, 0.01512292, 0.04631691, 0.01406473, 0.00363153, -0.03738434, 0.00811051, -0.00613233, 0.00280577, -0.00533737, 0.01800357, 0.00339862, -0.0014749, 0.02459372, -0.00032344, 0.02570175, -0.02019277, -0.03203687, -0.00085054, -0.01219257, -0.03222584, -0.03306592, -0.00292973, 0.01604269, -0.01971295, 0.06863094, 0.00273614, 0.03401838, -0.03325687, 0.00482941, -0.0350083, -0.02639041, -0.02887202, -0.01859399, 0.00230676, 0.00931039, 0.01249192, -0.01026674, -0.00994061, -0.04271205, 0.00109283, -0.02402648, 0.03740017, -0.02660162, 0.00865843, 0.02136979, 0.04008782, -0.00440417, -0.00150444, -0.03157016, -0.00710136, 0.00596188, 0.01298817, -0.02593902, 0.00768058, -0.02361924, -0.01484498, -0.0606806, 0.0011927, 0.03771246, -0.02030433, 0.02939528, -0.00027445, -0.00254482, -0.01360528, -0.02719941, 0.05516189, -0.00763776, 0.01727481, -0.02996933, -0.0051208, -0.03019707, -0.00567022, -0.02790305, -0.0181328, 0.00950341, 0.02476229, 0.03278505, 0.04032854, -0.00303066, -0.00131316, -0.01838839, 0.03972653, 0.02945684, 0.00506295, 0.04326555, 0.01061905, 0.02956143, -0.0260167, -0.0254177, 0.00069696, -0.00809019, 0.00277979, -0.01375313, 0.00349074, 0.01011342, 0.01872987, -0.01415245, -0.0132716, 0.00684813, -0.0349267, -0.01720277, 0.00125129, -0.04235349, 0.02792428, -0.02467248, 0.01441405, -0.05607369, 0.00722135, -0.00415876, -0.02306298, -0.00251564, -0.00613169, 0.0024343, 0.01416107, 0.0174195, -0.01672187, -0.00884946, -0.01232262, -0.03607734, -0.01303171, -0.00433565, 0.05991193, 0.011786, 0.01447398, 0.00084754, 0.02899997, 0.00100315, 0.00051968, -0.02260477, 0.03179467, 0.01271516, -0.0096492, 0.03056397, 0.00242623, 0.04521955, 0.01072991, 0.0115967, -0.0038018, -0.00714255, 0.00359719, 0.04203822, -0.02023698, 0.02507043, -0.01171852, -0.01900261, 0.03528672, 0.01415006, -0.02685401, -0.0052552, 0.01114532, -0.02915735, -0.00393814, -0.04668033, 0.04728321, 0.00227604, -0.04645975, 0.00248587, 0.01140569, -0.01427803, -0.00763763, -0.00998988, 0.00693708, 0.00738449, -0.01080612, 0.04048531, 0.02438805, -0.05108937, 0.02232452, 0.0172016, -0.02078298, 0.00206222, -0.04155487, 0.0039933, 0.00040598, 0.06202995, 0.00626049, 0.02450441, 0.00437956, -0.02372075, 0.02897209, 0.04808361, 0.01655706, -0.03428228, 0.00901206, -0.00034824, 0.02046406, -0.03260479, 0.01710199, 0.00148818, 0.03930174, -0.02218069, -0.04135638, 0.01040175, -0.01319111, 0.00989468, -0.01622834, 0.02281791, 0.00535794, -0.03543131, -0.02823762, -0.00956614, 0.01895612, -0.00392759, -0.00383996, -0.01788184, -0.02208056, 0.02672117, -0.02028035, 0.02406073, -0.03418796, 0.06011862, -0.02716701, 0.02176074, 0.01033257, 0.02353983, 0.02448397, -0.00939927, 0.01537328, 0.00328689, 0.00068993, -0.01787819, -0.03604862, -0.01989132, -0.00726599, 0.05619077, -0.01653346, 0.02691017, -0.0266909, 0.0177808, -0.00389139, 0.03568586, -0.01064045, -0.02513177, -0.00753622, 0.00501403, 0.02988672, -0.01436609, 0.01582894, 0.00394222, -0.01579356, -0.03216157, 0.00413346, 0.00979413, 0.01286936, -0.01108929, -0.03641632, 0.03385152, -0.0020903, -0.02322509, 0.01079103, 0.04407164, 0.01358412, 0.00409446, -0.00642543, -0.00506024, -0.04469349, 0.02142151, 0.01059807, -0.02359367, 0.00657059, -0.00146464, 0.00220173, 0.01502771, -0.01848167, 0.03636665, 0.00740306, -0.01316759, 0.0306688, 0.04082394, -0.00361007, 0.0001375, -0.00347301, 0.02015303, 0.00293696, 0.01071033, 0.00533204, -0.03502918, -0.03157046, 0.03717731, -0.0134869, -0.02389141, 0.0029892, -0.00198216, -0.00579039, -0.00113212, -0.00406987, 0.00275656, -0.02797523, -0.02460123, -0.00944705, -0.01359399, -0.0134911, -0.01101035, -0.04029605, -0.02654135, -0.021673, 0.02076587, 0.00692969, -0.03316529, -0.01897703, 0.00725323, -0.03388567, 0.01519617, 0.01876679, -0.03456262, -0.01609578, -0.02687844, -0.03009011, -0.01257614, -0.02730023, -0.02106455, 0.00801956, 0.0161728, -0.02686434, -0.00410583, -0.00234054, -0.0018898, 0.02197697, -0.01406289, 0.03297587, 0.0178876, 0.00412154, -0.01160098, 0.00682923, -0.03450947, -0.00084567, -0.00663143, -0.00551005, 0.0394866, 0.01797245, 0.0175772, -0.0245161, -0.00801153, -0.02699054, -0.00610363, -0.04050334, -0.01840977, 0.04194339, -0.01077953, -0.00821908, -0.0112594, 0.01918315, -0.01635482, -0.00933858, 0.00630071, 0.02800648, 0.00839709, 0.06121433, 0.02060777, 0.03674943, -0.02882053, -0.01126596, 0.01918505, -0.00650072, 0.00595162, -0.02718604, 0.00525207, -0.00103371, -0.03112809, 0.005374, -0.01454655, 0.02917048, -0.00594697, 0.00077034, 0.01070207, 0.05583637, -0.00892964, 0.02580322, -0.02236177, -0.01337486, 0.01680734, -0.03408115, -0.00092434, 0.00324752, 0.0190218, 0.00088312, -0.01080277, -0.00982951, -0.01707631, 0.01028286, -0.016904, -0.0306036, 0.01285526, 0.00708525, -0.00969872, 0.01984978, -0.02489595, -0.02166654, 0.01862, -0.05020455, 0.01487162, -0.00908468, -0.042232, -0.01076789, 0.01693954, 0.00719552, 0.01636087, 0.01102597, 0.00460196, -0.00739633, 0.00077187, -0.00553275, 0.00648658, 0.03202476, 0.01536515, 0.02122563, 0.01932298, -0.00311999, -0.02541202, -0.00593082, -0.02543801, 0.0406656, -0.01371176, 0.03237604, -0.02545489, 0.00014173, 0.02885908, -0.0163423, -0.00745056, -0.02318016, -0.00317646, -0.00104392, 0.0141577, -0.03731855, -0.01639799, 0.02492668, 0.03431095, -0.01290195, 0.00914957, -0.02351725, -0.00822783, -0.01438994, -0.00085314, -0.00046279, 0.00229037, -0.00512468, 0.02495812, 0.00095451, -0.01815813, -0.00515373, -0.01479167, -0.04334665, 0.02780291, -0.01237643, -0.0250224, -0.00912652, 0.00896415, 0.01711485, -0.00657095, -0.02351272, 0.00143077, -0.02238913, 0.0074484, -0.00049226, -0.01986329]}, "type": "document"}, {"id": "f32caf47-8c8d-4e93-80c9-f8327bc45369", "properties": {"page_content": "title: \"CEO Diversity Statement\" description: \"GitLab's CEO's <PERSON>'s vision for our Diversity, Inclusion, and Belonging core values.\"\n\npicture-of-gitlab-ceo-sid-sij<PERSON><PERSON><PERSON>\n\nGitLab believes in a world where everyone can contribute. As one of the company’s core values, Diversity, Inclusion and Belonging (DIB) is fundamental to the success of GitLab. Team member diversity leads to better decisions and a greater sense of team member belonging. When you have a diverse, inclusive and belonging environment where team members feel safe to show up as their full selves, you empower everyone to contribute.\n\nGitLab has a DIB Program which includes Team Member Resource Groups (TMRGs), which are voluntary team member-led groups, focused on fostering DIB within GitLab. I'm proud of team member driven initiatives such as mentoring for an advanced software engineering course at Morehouse College, a historically Black liberal arts school. We also do Reverse Ask Me Anything meetings in which I ask questions of TMRGs and get to learn from their experiences.\n\nWorking asynchronously, recording videos and writing things down rather than defaulting to in-person meetings, enables us to hire and work with people around the world from different cultures and backgrounds. We have created numerous DIB trainings both live and our own exclusive GitLab in-house DIB certification. We want to be known as one of the world’s greatest global companies that creates a unique approach to diversity in a remote environment.\n\n<PERSON>", "document_metadata": {"source": "Sample_Docs_Markdown\\ceo-dib-statement.md"}}, "type": "document"}, {"id": "e5fb375c-6a22-4cf5-acaa-f6dc4ef91786", "properties": {"page_content": "title: Diversity Inclusion & Belonging Communications Strategy description: PRoviding details on how the DIB Team communicates with GitLab to achieve engagement, contributions and collaborations from team members\n\nPurpose\n\nThe Diversity, Inclusion & Belonging (DIB) Team has a variety of ways that it communicates with team members from IC all the way up to e-group, this page documents the ways we do this, what information will be provided and the timing of when these communications will happen.\n\nEncouraging Participation in Diversity, Inclusion & Belonging\n\nDIB is a core value and is something that is measured by DIB Compentencies in performance reviews, mid reviews and promotions. The DIB team is providing an easily accessible way for team members to display the DIB value by providing on a monthly or quarterly basis depending on level, way you can contribute on a increasing scale of Good, Better & Best. This will enable team members to participate in a ways they are able to at any given time depending on competing priorities.\n\nDRIs & Dates & Method of Communication:\n\nE-Group Communications -\n\nDRI: <PERSON>rida <PERSON>llan\n\n<PERSON>ce: Quarterly\n\nMethod of Communication:\n\nVP+ Communications -\n\nDRI: <PERSON><PERSON>: Quarterly\n\nMethod of Communication: VP Direct Monthly Call\n\nLeadership DIB Council -\n\nDRI: <PERSON>: Monthly\n\nMethod of Communication: Leadership DIB Council Call\n\nPeople Managers Communications -\n\nDRI: <PERSON>: Monthly\n\nMethod of Communication: #People-Mrgs+ Slack Channel\n\nIndvidual Contributors Communications -\n\nDRI Marina <PERSON>rigg\n\nCadence: Monthly\n\nMethod of Communication: While you were Iterating, What’s Happening and DIB Slack Channels\n\nExample\n\nGood: Use monthly Zoom Background attached Better: Promote & Attend heritage month event on XXXX Best: Post in the #people-managers-and-above around a personal story focused on your experience with regards to XXXX\n\nWhy?\n\nWe are approaching it this way to ensure that all team members have the same opportunity to contribute to Diversity, Inclusion & Belonging regardless of their capacity to contribute. This will help provide clear and concise ways that team members can live in the DIB Value\n\nLogistics\n\nE-Group Communications - Reminder: week 8 of prior quarter Content Ready & Reviewed: 1 week prior to the start of the quarter Released:\n\nVP+ Communications - Reminder: week 8 of prior quarter Content Ready & Reviewed: 1 week prior to the start of the quarter Released: During the VP Directs Call\n\nLeadership DIB Council - Reminder: 1 week prior to LDC Call Content Ready & Reviewed: 3 days prior to the LDC Call: 1st week of the new month\n\nPeople Managers Communications - Reminder: 1 week prior to the start of the month Content Ready & Reviewed: 3 days prior to the start of the month Released: During the LDC Cal,\n\nIndvidual Contributors Communications - Reminder: 1 week prior to the start of the month Content Ready & Reviewed: 3 days prior to the start of the month Released: 1st week of the new month\n\nDIB Monthly Initiatives Call\n\nWe host the DIB Monthly Initiatives Call to provide an opportunity for Team Members to be able to ask questions around DIB Initiatives, DIB Programming and general questions regarding DIB. We also provide a deck where we highlight everything that happened in the last month and what to look forward to.\n\nProcess\n\nDRI: Marina Brownrigg, DIB Partner\n\n10 working days prior to the monthly DIB Initiatives call, create Issue, then tag all the TMRG and TMAG stakeholders/leads to add their updates in the linked deck prior to the call.\n\nDIB in Q2 - PBPs Only\n\nTo ensure strong collaboration with the People Business Partner team we provide a DIB in Q2 deck that highlights the strategic imperatives of the DIB Team and any collaborations that are required.\n\nProcess\n\nDRI - Liam McNally, Manager, DIB\n\nCadence: Quarterly\n\nRelease for Collaboration from DIB Team: 2 weeks prior to the end of the quarter\n\nDelivered: First week of the new quarter\n\nMethod of Communication: Leadership DIB Council Call DIB-PBP Slack Channel", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-communications-strategy.md"}, "headlines": ["Purpose", "Encouraging Participation in Diversity, Inclusion & Belonging", "DRIs & Dates & Method of Communication:", "Why?", "Logistics"], "summary": "The Diversity, Inclusion & Belonging (DIB) Team at GitLab communicates with team members across all levels to encourage engagement and contributions. The team provides monthly or quarterly opportunities for participation categorized as Good, Better, and Best actions. Communications are managed through various channels such as E-Group meetings, VP calls, Slack channels, and the Leadership DIB Council. Each communication stream has a designated lead (DRI), cadence, and method of delivery. Additionally, the DIB Monthly Initiatives Call allows team members to ask questions and review updates. Contributions are aligned with performance reviews to emphasize DIB as a core value. Logistics include reminders, content reviews, and releases according to set timelines. A quarterly DIB in Q2 deck fosters collaboration with the People Business Partner team.", "summary_embedding": [0.06797047, -0.10494094, 0.0661748, -0.02695069, -0.01839659, -0.05341078, -0.05150342, -0.07521612, 0.09244797, 0.00086449, -0.01239284, 0.0884744, 0.05173917, -0.06718224, 0.11487748, -0.04356226, -0.11687128, 0.10987534, 0.02986319, -0.01881901, -0.01881165, -0.09714057, -0.086279, 0.05854125, -0.04204642, -0.00596663, 0.05354352, -0.03631346, 0.06136701, -0.01596035, 0.18225166, -0.00650898, -0.04319217, 0.07615561, 0.00086151, 0.04793351, -0.00685097, 0.02983681, 0.02757825, -0.01928353, -0.00226102, 0.04081318, -2.623e-05, 0.01121096, 0.12177319, -0.03874245, 0.01044093, 0.0217896, 0.04226325, -0.06400283, 0.0648365, 0.10357831, 0.1224914, 0.03759911, -0.00750513, 0.09717445, -0.01793611, -0.04456034, 0.01551299, -0.02464442, -0.03008028, 0.02887596, -0.02253843, -0.0751181, 0.0190236, 0.09716109, 0.02256818, 0.00083365, -0.03562318, -0.03957961, 0.01529032, -0.01685795, -0.0618406, 0.09532934, -0.01010102, 0.02313687, 0.01153435, -0.04183704, -0.06328459, 0.01042864, -0.01420314, 0.01710758, 0.05157959, -0.03054712, -0.02412091, -0.0367383, -0.08099686, -0.02536646, -0.04264597, 0.0536513, 0.07172845, -0.03775406, -0.04991081, 0.0165255, -0.02861825, 0.04367317, 0.0510864, -0.04279723, 0.05445557, -0.04900637, 0.05736328, 0.00751207, -0.04955673, -0.04292507, 0.03343685, 0.00091924, 0.11619797, -0.02333093, 0.03914857, -0.06929708, -0.00994369, 0.09183798, -0.01243457, -0.00037832, 0.02260895, -0.04257131, -0.00599033, -0.00529143, -0.01736903, -0.07146693, 0.03978844, 0.03838715, -0.07079681, 0.0276256, 0.01723456, -0.03375256, -0.02652127, 0.07132462, -0.00815255, -0.03231628, -0.01750853, 0.00950649, 0.05289992, -0.06172474, 0.00648462, 0.03656204, 0.00370737, -0.03167792, -0.02754333, 0.00921809, -0.03927781, -0.02743698, -0.0409121, 0.0555924, -0.04831146, 0.00519925, 0.00117155, -0.00328329, -0.03678913, 0.05475257, 0.00303457, 0.04183516, -0.03648388, 0.0086166, -0.03457528, -0.02021608, 0.00648575, 0.01179334, 0.05348514, 0.01439032, 0.0414495, -0.00888659, -0.00079278, -0.03307041, 0.03657059, -0.02901108, -0.0191384, 0.05281112, 0.03245989, -0.02518841, 0.00623746, 0.06747439, -0.0045561, 0.01975, 0.01193894, -0.05634015, -0.03812534, 0.0259174, -0.00705791, 0.04659505, -0.0263804, -0.01674408, -0.02209092, 0.0031504, -0.01644733, 0.04587514, 0.01357566, 0.01343168, -0.03136437, 0.03433432, -0.04648408, 0.0177926, 0.01611912, -0.00012009, -0.07024737, 0.0254472, -0.0275391, -0.01214561, 0.00234869, 0.01019683, 0.05518457, 0.01833373, 0.02282676, 0.00427424, 0.05451316, 0.02771711, 0.00225667, 0.00982007, -0.00011064, -0.00230681, -0.02490954, 0.05146635, 0.0215802, 0.01425776, -0.00822487, 0.04728884, -0.00509463, 0.03087214, 0.02722153, -0.05086059, -0.05952387, -0.00451331, 0.03682158, 0.02937951, 0.00468348, -0.05261147, -0.05502069, 0.03991175, 0.04256246, 0.01772278, -0.05191611, 0.00084083, 0.04549858, -0.01487728, 0.01577652, -0.07428945, 0.04697963, -0.01332133, 0.03908888, 0.00887854, 0.03175397, -0.06912032, 0.01457562, -0.03012278, 0.00935123, 0.0190931, 0.00422931, 0.05724607, -0.07878777, 0.00046603, -0.07132662, -0.04500351, -0.01072764, -0.03927335, -0.01730555, 0.01433435, 0.02704718, -0.04836455, -0.03682577, 0.03908482, -0.0040825, -0.01056792, 0.00360033, 0.01199252, 0.06043823, 0.04831796, -0.00983323, -0.00595163, -0.03063745, 0.00135367, -0.01319509, -0.01022795, -0.0337293, -0.01486821, -0.01542958, -0.00268514, -0.00037086, -0.02512957, 0.02441801, -0.0143891, -0.06926554, 0.007266, -0.01019272, 0.01217375, -0.03501882, 0.00373729, 0.06007472, -0.025549, -0.03598493, 0.00071858, 0.02916368, -0.00076566, -0.01607127, -0.00324453, 0.0256378, 0.00046651, 0.00437171, 0.00107635, 0.01474368, 0.00682049, -0.03745082, 0.01906063, 0.02189065, -0.03131185, 0.02135416, -0.03796246, 0.00620112, -0.05904301, -0.02437649, -0.00360658, 0.02825775, 0.04231437, -0.0238446, -0.0122563, 0.01520119, -0.0050962, 0.00585598, -0.0147209, -0.00681275, 0.00309833, 0.00051825, 0.01675412, 0.00912138, 0.00945717, 0.01763804, 0.00274531, -0.02092694, -0.05654359, -0.03067186, 0.01982669, -0.01537638, 0.02967895, 0.00499642, 0.01220005, -0.00221522, -0.06461487, -0.03090725, -0.00587457, 0.00733964, 0.03804503, -0.01864518, 0.00775774, 0.02015228, 0.0126663, -0.00018213, -0.01159438, 0.00329293, -0.0199749, 0.01841783, -0.00167092, -0.0471251, -0.02337885, -0.03706693, -0.01707729, 0.03466124, 0.04921003, -0.04767872, 0.01337927, -0.0193029, 0.01287661, 0.04132108, -0.01323893, -0.01504276, 0.04351759, -0.02725644, -0.02647338, 0.02974348, 0.02767588, -0.01849944, 0.0389166, 0.03996786, 0.0019551, -0.01340523, 0.04228092, -0.00035934, 0.00288784, 0.03150836, 0.00280081, -0.00869851, -0.00060781, -0.00756206, 0.00781306, 0.01509058, 0.00086326, 0.03153444, 0.00202921, 0.04060397, -0.02377614, -0.00549742, -0.00354648, 0.05485643, 0.02984171, -0.03072767, 0.02920229, -0.04025595, -0.03949922, -0.04291892, -0.02307088, 0.00726105, 0.05268086, 0.03419212, 0.0519347, 0.02935657, -0.06645996, 0.02929985, 0.00801553, -0.00146317, 0.01356423, -0.0722601, 0.00056353, 0.01523624, 0.01585143, 0.00333707, 0.03969943, -0.02438067, 0.03814715, 0.02234597, 0.02927602, 0.00411888, -0.00694055, 0.00101571, -0.02582837, 0.0210092, 0.00174996, 0.01354366, -0.00582033, -0.01412731, -0.03252246, 0.06384261, -0.02583926, 0.03993027, 0.02065356, 0.01287649, -0.0429963, -0.02556775, -0.00208388, 0.05144611, 0.02573985, 0.02919043, -0.02816162, 0.06097981, 0.04282391, -9.66e-06, -0.00989145, -0.03423126, 0.01731553, -0.01720479, -0.02159684, 0.00979459, -0.02118927, 0.00673963, 0.04780615, 0.04967996, 0.02969193, 0.03041105, 0.01565476, -0.00525085, 0.0065933, -0.01225574, 0.03596913, 0.00836435, -0.0443676, 0.05040896, -0.01491778, 0.01884162, 0.03545071, -0.00147429, 2.94e-05, -0.00587471, 0.04192535, 0.03403611, -0.01504006, -0.02245202, 0.00882016, -0.03270745, 0.03814986, -0.02202263, 0.05044487, -0.01284849, -0.01274962, 0.0245967, 0.00567564, -0.02892656, -0.00474728, -0.02152905, -0.06187028, -0.00942126, 0.02963423, -0.0385587, -0.02700806, -0.00261389, -0.01109563, -0.00889598, 0.04352546, -0.06919312, 0.03937107, -0.02126465, -0.00814207, 0.01665228, -0.01453334, -0.01242195, 0.02315259, -0.03805708, -0.02127083, -0.02843748, -0.05061163, 0.00085463, 0.01162923, -0.00639403, 0.06939594, -0.04579307, -0.04126225, -0.00163027, -0.06036352, 0.031095, 0.00932936, 0.02032882, 0.00586239, -0.01084191, -0.01053642, -0.02808494, 0.00107519, -0.00600803, 0.03770085, -0.00290822, -0.01172632, 0.03835711, -0.00914398, -0.02070301, -0.01554982, -0.02057716, 0.04065158, 0.01104983, 0.01475065, -0.03128187, 0.00983318, 0.00367789, -0.03379828, -0.01936591, 0.0256963, -0.00569548, -0.03646895, -0.02543365, -0.0109904, 0.00098288, 0.02542776, -0.01143914, -0.04314453, 0.07094926, 0.04123081, -0.03217999, 0.03642514, 0.00906871, 0.014125, 0.00688079, -0.00031372, -0.02135473, -0.02301645, -0.02419047, 3.223e-05, 0.01593025, 0.01094683, 0.0127356, -0.05367605, 0.01807793, -0.01206974, 0.00591765, 0.02840594, -0.06254265, -0.02029849, 0.01863712, 0.01428429, 0.01371514, -0.00645965, 0.0010173, -0.00343174, 0.00203125, 0.01370478, 0.01009799, -0.03357826, -0.00299221, -0.01725226, 0.00934974, -0.0003769, -0.02865886, -0.03845023, -0.02308129, -0.01579187, -0.01671533, -0.03753078, 0.00239554, 0.03198307, -0.0345738, 0.05480913, -0.01856723, 0.0228895, -0.03471841, 0.00290454, -0.0478507, 0.00671883, 0.00166247, -0.00597649, -0.0105667, 0.01009831, -0.00206969, 0.02764739, -0.0008133, -0.0255858, -0.01747408, -0.01873424, 0.01495721, 1.78e-05, 0.00837797, 0.01676433, 0.05107704, -0.0109096, 0.01352238, -0.01946045, 0.00190801, -0.00621456, -0.00666079, -0.03253872, 0.02291863, -0.01046167, -0.02399763, -0.03285688, -0.00499817, 0.03602733, -0.05144479, 0.02341805, 0.01030608, 0.01322296, -0.02317725, -0.02914202, 0.04124955, -0.03272471, 0.03783236, -0.01830423, -0.01510335, -0.03442887, -0.00129075, -0.03031958, 0.00767767, -0.02108836, -0.00770007, -0.02682243, 0.0288019, -0.00182429, 0.01929135, -0.02285151, -0.0135852, 0.00739786, 0.0064266, 0.02915701, -0.00388001, 0.02524162, -0.00705377, -0.01369295, 0.01108351, -0.01206077, 0.00970223, -0.03828929, 0.01832832, -0.00474396, -0.01008921, -0.02890876, -0.01327186, 6.605e-05, -0.03119288, 0.0182344, -0.01172026, -0.04716729, 0.01109472, 0.01027848, 0.01015031, -0.04626976, -0.00692487, 0.00097044, -0.00281271, 0.00437738, -0.00499602, -0.04222728, 0.02382634, 0.04525067, -0.00973998, -0.03872738, -0.00472798, -0.00971073, 0.02397499, 0.02139178, 0.03626806, -0.00416365, 0.02730048, -0.01319034, 0.03280174, -0.01108217, -0.00753316, 0.01209542, -0.00855089, 0.00071933, -0.01388934, 0.03098911, -0.00791573, 0.02806021, -0.00841825, 0.03623374, 0.00797011, -0.01479262, -0.00474579, 0.03845485, -0.02799071, 0.02176743, 0.02151952, -0.03102889, -0.00173726, 0.00332908, -0.02443824, -0.00621481, 0.0017817, -0.03668433, 0.01299878, -0.03427711, 0.04705196, 0.01146771, -0.0592289, 0.00935148, 0.02478137, -0.00898628, -0.0151971, -0.00021704, -0.00979509, -0.0496454, -0.01259568, 0.04685331, 0.00823054, -0.04657338, 0.03519598, -0.00014168, -0.01741064, -0.00462518, -0.03843319, 0.01579607, -0.01010991, 0.05267106, 0.02175674, 0.002286, -0.01434663, -0.02037688, 0.05427026, 0.0038558, -0.00074013, -0.03020681, 0.01268713, -0.01059059, 0.00157148, -0.00946558, 0.02681494, 0.01152969, 0.00294813, -0.01297393, -0.01756821, -0.00836012, -0.00324383, -0.02856639, -0.01916351, -0.01662203, -0.00716523, -0.02505306, -0.0346529, 0.01536275, 0.00364985, -0.01519363, -0.00700218, -0.00726711, 0.02752309, 0.02434315, -0.01560437, 0.01051072, -0.02751957, 0.02084374, -0.01711968, 0.02341251, -0.00527599, 0.02050861, 0.04382763, -0.03147667, -0.00592846, -0.01091633, -0.00287138, -0.02663323, -0.0175451, -0.04048759, 0.02059835, 0.03528365, -0.02332066, 0.02162476, -0.01681226, 0.00072439, 0.00157075, 0.03892632, -0.00855442, -0.01011202, -0.00812779, 0.00457406, 0.0331736, 0.02723145, 0.0160742, 0.00365434, -0.02762209, -0.02528835, -0.00081766, -0.01040086, -0.00344983, -0.0337716, -0.00696498, 0.01080109, -0.00569661, -0.00593225, 0.00937229, 0.02947891, 0.01794221, 0.02129364, 0.01866928, 0.00306905, -0.01708655, 0.0401002, -0.00250787, -0.0124685, 0.01819113, 0.0099715, 0.03173532, 0.00458344, 0.02244153, 0.03728429, 0.00265345, -0.03096476, 0.03762584, 0.01910332, 0.01316527, -0.00085426, 0.02035143, 0.01248806, -0.00840524, 0.00829611, -0.0154144, -0.03690045, -0.02118967, 0.05676707, -0.026688, -0.01916827, -0.00037236, 0.02027692, 0.0020688, 0.00617309, -0.01175926, -0.01310492, -0.05662923, -0.0246545, 0.02308924, -0.00616676, -0.03606028, 0.0030843, -0.04643032, -0.00037308, 0.00226731, 0.02723267, -0.00641249, 0.00548624, -0.02759775, -0.00998728, -0.00367152, -0.00047923, 0.02300159, -0.01274798, -0.01503071, -0.00906648, -0.01050118, -0.02434125, -0.02754172, -0.01876899, -0.00176442, 0.00635554, -0.01325497, -0.01136044, -0.0194735, -0.00130377, 0.03180855, -0.02332821, 0.04146826, 0.01748245, 0.00755566, -0.00220623, 0.01338208, -0.02358043, 0.00028762, 0.00580574, 0.00964128, 0.02787754, 0.00765473, -0.00551402, -0.00116911, 0.01635243, -0.01073324, -0.00888704, -0.05770982, -0.0107391, 0.01480895, -0.01287359, -0.01888748, -0.0244765, 0.03899106, -0.00467502, 0.00504998, 0.01240796, 0.0014021, -0.01064106, 0.0604698, 0.0151321, 0.02324766, -0.00152393, -0.01043158, 0.00964391, -0.00224549, 0.00867666, -0.00779988, 0.00706047, -0.00246537, 0.00149617, -0.00169436, -0.00208922, -0.02157113, 0.02289606, -0.0020164, -0.01007822, 0.04658185, 0.00074984, -0.0018874, -0.01337903, -0.01713636, -0.00507113, -0.04635731, 0.01029883, -0.01635501, -8.768e-05, -0.01517291, -0.00338111, -0.00624098, -0.00685062, -0.00368883, -0.01494935, -0.03598458, 0.04945093, -0.01236574, -0.01957308, 0.00867462, 0.0028053, -0.04947693, 0.00278071, 0.00385718, 0.02937069, -0.02399046, -0.02711007, 0.02165371, 0.02529706, 0.05057017, 0.01627723, -0.03798836, 0.01632139, -0.03210348, 0.02450759, -0.02394188, -0.00331002, 0.01072623, 0.01239341, 0.0070217, 0.04827769, -0.0177302, -0.00236635, -0.00032409, -0.00701793, 0.01934899, -0.00262227, 0.02572818, 0.00133373, 0.02865548, 0.00313827, -0.00708027, -0.01912789, -0.02590391, 0.00969141, 0.028759, 0.02818935, -0.06175613, -0.0139698, 0.02163432, 0.01947884, -0.00361984, 0.00407343, -0.02237936, -0.0036008, 0.01124219, -0.02705728, -0.00779921, 0.00753499, -0.03587866, 0.00765634, 0.0095334, -0.01024347, -0.00921534, 0.01565028, -0.04650006, 0.04065736, 0.01251307, -0.01164644, 0.01402762, 0.01231175, 0.01563683, -0.01346989, -0.00231661, -0.03404611, -0.03808736, 0.00538173, 0.00623989, 0.00112736]}, "type": "document"}, {"id": "5914dd10-3247-46da-ad53-b83ae4c38a09", "properties": {"page_content": "title: \"Program of Events\" description: \"This page will provide a calendar of upcoming events & program release for Diversity, Inclusion & Belonging.\"\n\nThis page will provide a calendar of upcoming events & program release for Diversity, Inclusion & Belonging.\n\nMonth Events & DRI Notes January - Release Neurodiversity in the workplace Short Course - <PERSON> February - Black History Month - <PERSON> March - International Women's Day - Women's History Month April May June July August September October November December January", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-events-program.md"}}, "type": "document"}, {"id": "f05baa18-72e8-425a-ae09-807d01931d66", "properties": {"page_content": "title: \"Goals\" description: \"This document provides leadership and team members visbility on the Diversity, Inclusion & Belonging Goals\"\n\nIn this page you will find the current Diversity, Inclusion and Belonging Goals for GitLab. These will be updated on a quarterly basis to ensure they remain current. We will keep a list of completed or non-current goals at the bottom with a retention of 12 months. If you would like to see the history of goals going further back, you can view the evolution of the page in GitLab.\n\nCurrent Goals\n\nGeographic Diversity (Team Member representation outside of NORAM)\n\nWe do not currently have a company wide goal for Geographic Diversity and this will remain in place for FY'23. Instead we will focus on increasing Director+ representation outside of the United States as team member representation is currently outpacing leadership representation. It would not prove advantageous to take a goal to contiue to increase team members external to the United States without first focusing on increasing our leadership population external to the United States. In FY24, we will reevaluate the need for director level+ representation goals outside of the United States.\n\nProblem Statement & Data:\n\nWhat is a timeline to create a US leadership KPI and/or OKR?\n\nHypothesis:\n\nHiring occurs predominantly in US.\n\nLeadership is predominantly US Centric\n\nWe are not currently hiring in enough countries\n\nData:\n\nEngineering Data: Representation in US vs. External to US\n\nBreakdown by levels showed that there are proportionate # of TMs at both the IC and Manager levels when comparing US to Non-US representation.\n\nEngineering: US: 30.2% Non-US: 69.8%\n\nResults:\n\nBased on the data provided on both the current breakdown of TMs, levels and regions for Engineering, we do not recommend creating an additional goal around increased geographic representation.\n\nGlobal Representation of URGs\n\nThese breakouts are currently found here\n\nPast Goals\n\nCurrently no past goals to attribute here\n\n**Due to data and or legal limitations, this is not an exhaustive list of all of our underrepresented groups. Those with disabilities, those that identify as LGBTQIA+, etc. who choose not to disclose or underrepresented ethnicities outside of the US.\n\nThe DIB Team is actively working on finding data sets outside the US and inclusion metrics for underrepresented groups we cannot report on as team member representation.", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-goals.md"}}, "type": "document"}, {"id": "75069c2c-5b1e-4844-b8eb-0d44238cfdd3", "properties": {"page_content": "title: \"Roundtables\" description: On this page you will be provided an overview of our Diversity, Inclusion and Belonging Roundtables.\n\nA DIB roundtable is a great way to build deeper connections with team members and develop safe spaces to discuss DIB related issues. The DIB roundtable will ask team members to share stories and anecdotes as well as challenge team members to think about how they personally and collectively can positively impact DIB.\n\nWe have two avenues for DIB Roundtables:\n\nDIB Programmed Roundtables - The DIB Team creates a programmed roundtable(s) on a quarterly basis to discuss a pre-defined topic.\n\nSelf-Organized - A TMRG, Team Members, Managers can organized a roundtable to discuss DIB related issues.\n\nStarting a roundtable\n\nDiversity, Inclusion & Belonging Team Programmed\n\nThe DIB Team will organize DIB Roundtables on a quarterly basis with a maximum of two roundtables in a quarter.\n\nThe DIB Team will program the quarterly roundtable on the team calendar when the decision has been made on the topics for the quarter\n\nThe DIB team will create an agenda with a series of questions for the quarterly roundtable\n\nThe roundtable will last 50mins and be split as followed:\n\n10mins: DIB Facilitator will set the topic and provide some context\n\n30mins: The DIB Facilitator will divide the group into small groups of 5/6 team members to discuss the questions in the agenda\n\n10mins: Debrief\n\nThe DIB Team will ask for a volunteer or are elect group facilitator for each group, your role is to:\n\nVerbalize the questions\n\nEnsure that everyone, including yourself gets an opportunity to contribute\n\nShare your screen where necessary i.e. slides or tasks\n\nProgrammed Roundtables:\n\nDate & Times Subject DIB Facilator TMRG (if applicatble) TBC Allyship Liam McNally N/A TBC Bias Liam McNally N/A TBC Microaggressions TBC NA\n\nSelf-organized\n\nFirst, identify a group of team members who would like to participate. This can be done via slack, with your direct team or other avenues.\n\nOpen an issue using this template and invite the roundtable members\n\nAdd DIB Roundtable Label\n\nTag @gitlab-com/people-group/dib-********************and-belonging for visibility\n\nAssign or elect someone who will help facilitate the conversation\n\nIf you volunteer or are elected to be the facilitator you role is to:\n\nVerbalize the questions\n\nEnsure that everyone, including yourself gets an opportunity to contribute\n\nShare your screen where necessary i.e. privilege for sale task\n\nManager Organized\n\nA manager can request the DIB Team organize a DIB Roundtable for their team. They will help facilitate the roundtable and will organize other activities specific to the needs of the team.\n\nIf you are a manager, request a DIB roundtable by using this issue template.\n\nA DIB Team Member will set up a time to discuss with the manager the function of the DIB Roundtable and suggest activities and exercises to achieve the aim of the Roundtable.\n\nThe Roundtable\n\nSet the ground rules\n\nThis is a closed session and a safe space. However if something arises as part of the conversation that needs investigation, the roundtable facilitator will reach out to their PBP to discuss.\n\nAssume positive intent, share and express with a higher intent\n\nLearning happens through experience; so if someone makes a mistake, give feedback kindly\n\nPlease avoid multitasking unless there is a true emergency; please give your full attention to the session.\n\nStart with Privilege for Sale or Privilege Backpack\n\nPrivilege for Sale is a great interactive activity to get you thinking about Diversity, Inclusion and Belonging. Click the link for the full activity.\n\nThe Goals and learning objectives are as follows:\n\nTo acknowledge and investigate privilege.\n\nTo provide an opportunity for team members to connect and reflect on the experience of having (or not having) privilege.\n\nTo discuss the variety of privileges that underrepresented groups have limited access to. Not just legal privileges but social, financial, etc.\n\nTo discuss how no one privilege is more important than another, that for someone any privilege may feel essential.\n\nTeam members will be able to identify privileges that they take for granted in their everyday life.\n\nTeam members will discuss what types of privileges (social, financial, legal, etc.) are important to them and why that may differ from others in their group.\n\nTeam members will be able to investigate and discuss what groups may have limited access to what privileges and effect that lack of access may have on an individual.\n\nIn the activity you have a list of privileges, you do not have any of these privileges, for the purpose of this exercise.\n\nRoundtable Questions\n\nWhat are some of your most important values?\n\nWhat resonates with you?\n\nShare a time when you felt like you were left out or didn’t belong?\n\nWhat are some of the common threads, behaviors that made you feel like this?\n\nShare a time where you felt empowered or belonged?\n\nWhat were the actions/behaviors that led to you feel empowered or that you belonged?\n\nWhat does Diversity, Inclusions and Belonging mean to you?\n\nWhy is it important?\n\nWhat is one thing as a group can we do to make a positive impact on Diversity, Inclusions and Belonging?\n\nOnce you have fully completed the roundtable, create a handbook entry on the DIB Roundtable Sharing Page. You can share as much or as little as your would like too about the experience and learnings of the roundtable.\n\nWhat next?\n\nThe Diversity, Inclusions and Belonging Team is working on Roundtables part two, where we will go beyond creating safe spaces to discuss DIB related issues, to developing deeper trust, empathy and vulnerability.", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-roundtables.md"}, "headlines": ["DIB Programmed Roundtables", "Self-organized", "Manager Organized", "The Roundtable", "What next?"], "summary": "DIB Roundtables aim to foster deeper connections and safe spaces for discussing Diversity, Inclusion, and Belonging (DIB) issues. There are two types: Programmed Roundtables, organized quarterly by the DIB Team with predefined topics, and Self-Organized Roundtables, initiated by team members or managers. Programmed roundtables include activities like 'Privilege for Sale' and involve small group discussions facilitated by volunteers. Managers can also request custom roundtables for their teams. Ground rules emphasize safety, positive intent, and active participation. Key questions guide discussions on values, experiences of exclusion or empowerment, and personal interpretations of DIB. Afterward, participants document their learnings in a handbook entry. Future plans include advancing trust and empathy in part two of the roundtable series.", "summary_embedding": [0.05595447, -0.11851075, 0.09080578, -0.03371758, -0.03119347, -0.06627148, -0.00824284, -0.0130687, 0.11284245, 0.02051619, 0.04913864, 0.1365232, 0.04105316, -0.05171129, 0.11921632, -0.07471006, -0.09326021, 0.06527396, 0.01878063, -0.01456244, -0.00971525, -0.03649078, -0.16005117, -0.03347047, -0.03325297, -0.02376051, 0.02430336, -0.04122, 0.02575585, -0.08936448, 0.21340305, -0.05892297, -0.03634049, 0.06723872, 0.02428324, 0.00735335, 0.03621738, 0.03906198, 0.0336304, -0.01172192, -0.10968582, 0.02151329, 0.00744189, 0.00837418, 0.03289583, -0.04390335, -0.03909678, -0.00338641, 0.04130604, -0.06678196, -0.01349146, 0.03052065, 0.03472423, 0.02458428, -0.05456718, 0.07355757, 0.00349019, -0.04551864, 0.02823073, -0.07014158, -0.00359717, 0.04174944, -0.03042264, -0.0397202, 0.00485702, 0.07372136, 0.03324918, -0.06586696, -0.03613308, 0.00607008, -0.03602605, 0.00632016, 0.000303, 0.06615222, 0.00192052, 0.05879422, -0.05930461, 0.04787637, -0.07103566, -0.04871684, 0.0243228, -0.05108985, -0.06730352, -0.03876343, -0.00581366, -0.01892259, -0.0615157, 0.04218562, -0.03003582, 0.043532, 0.08226854, 0.00328579, -0.02046234, -0.01201698, -0.0301708, 0.05798941, -0.01092093, -0.08759119, 0.07249013, -0.03022088, 0.04375025, 0.02527938, -0.01452964, -0.03628068, -0.00463036, 0.00331573, 0.08650211, -0.03505539, 0.00871211, -0.04117631, -0.01925247, 0.08199139, 0.06067455, -0.03404586, -0.01926929, -0.00437964, -0.0076359, 0.00762045, 0.01195024, -0.04792417, 0.01828066, 0.04365061, -0.03339379, 0.0180599, -0.06339391, -0.10094607, 0.01810077, 0.05247388, -0.041005, -0.03865013, 0.02454888, -0.01416586, 0.05681922, -0.01660963, 0.00258716, -0.02298901, -0.03596281, -0.06238341, -0.03304514, -0.01885194, -0.01916084, 0.01776959, 0.01501142, 0.01860703, -0.038674, 0.00293378, -0.02896773, -0.04801516, 0.03034089, 0.0794485, 0.01525528, 0.01922265, -0.02459725, 0.01184776, 0.0063972, -0.00933574, 0.0422515, 0.00049902, 0.01951706, -0.00829653, 0.02087695, -0.03009891, -0.00174504, -0.01886533, 0.00926479, 0.01183402, -0.00543041, 0.03135137, 0.04182887, -0.03320964, 0.02839704, 0.09086235, -0.03889686, 0.01830237, 0.03450281, -0.02665661, -0.03406232, -0.02337626, -0.03172372, 0.06483939, 0.00325875, 0.01422951, -0.04277961, 0.06290343, 0.06238464, -0.00549052, -0.02074086, 0.003017, 0.01001369, 0.08770265, -0.0253441, 0.03217777, -0.02386145, -0.00284503, -0.04980723, -0.03472705, -0.02366447, -0.0373982, 0.01991834, 0.00171286, 0.03428006, -0.00279527, 0.00089148, -0.0155477, 0.05301378, 0.03587383, 0.01699072, 0.01001272, -0.07419401, 0.02624425, 0.0194781, 0.01909495, 0.01065655, 0.00405902, -0.02271839, -0.00357963, 0.04600609, -0.00468935, 0.03352613, -0.04660089, -0.06152726, -0.06324509, -0.0067602, 0.03162071, -0.026053, -0.01418182, 0.0005584, 0.02261343, 0.04376848, 0.02234942, -4.088e-05, -0.03974929, 0.01890467, 0.03430127, -0.03140323, -0.04427495, 0.02963089, -0.0004489, 0.03789534, -0.02626652, 0.00988785, -0.02050742, 0.01649171, -0.03502876, 0.01503357, 0.03322966, 0.05529225, 0.0236187, -0.03652795, -0.02006757, -0.0383516, -0.06007966, -0.03427336, -0.04325767, 0.00884697, -0.03034982, -0.00528756, -0.06109863, -0.05151934, 0.06532253, -0.00993585, 0.01527954, 0.01908552, -0.00382916, 0.03575709, -0.00706888, -0.0019341, -0.02261578, -0.04994223, 0.00352607, 0.00626311, -0.01338727, -0.0187008, -0.02361846, -0.02452539, -0.00388184, 0.0125849, -0.05217837, 0.01284549, -0.02331074, -0.02346622, 0.01838831, -0.03552197, -0.00506123, -0.03546993, 0.01901225, 0.0079248, -0.00918408, -0.02226321, 0.00921096, 0.02164613, 0.02145996, -0.02350322, 0.00506302, 0.01556459, 0.00947968, -0.01260689, 0.00123797, 0.0192043, 0.00642457, -0.055463, 0.00811707, 0.04099576, -0.05727937, 0.00431399, -0.02134323, -0.02297342, -0.02325289, -0.03587849, -0.03644048, 0.01027249, 0.0281228, -0.00623896, -0.00583806, 0.05096352, 0.01960208, 0.0074561, 0.02762393, 0.01065533, -0.00462863, -0.00603462, 0.00087485, 0.0215314, -0.00364826, -0.03174461, 0.01979724, -0.0396814, -0.01778953, -0.00936097, 0.0263757, -0.01649307, 0.01849267, -0.01015848, 0.04931635, 0.01188565, -0.06519334, -0.01595744, -0.06720707, -0.01763628, 0.01255229, -0.02614466, -0.02356119, 0.03680797, -0.00853842, -0.00189549, -0.031871, 0.00312225, -0.04178141, 0.02907163, -0.01436219, -0.03768566, 0.0034334, -0.03539071, -0.02999039, 0.00370287, 0.04457041, -0.02976439, -0.01666618, -0.00867529, -0.00036833, 0.04886568, 0.01316039, 0.00042681, 0.05393441, -0.02628784, -0.04748067, 0.07569077, 0.00243191, -0.05615356, 0.03621652, -0.00215504, -0.03147645, -0.01544428, -0.01550342, 0.01816264, 0.00731765, 0.04770529, 0.03953646, -0.01907334, 0.00690444, -0.02085449, 0.00284387, 0.00907439, 0.02222968, 0.04103068, -0.00440987, 0.04535827, 0.00905153, 0.00455209, -0.05179979, 0.05589819, 0.04560436, -0.00853785, -0.01675402, -0.0080757, -0.01628059, -0.0309, -0.03167239, -0.02263453, 0.06804024, 0.03808226, 0.00429517, -0.04050503, -0.05499035, 0.02046741, 0.00706461, -0.01942327, -0.02338228, -0.04819352, -0.00858757, 0.01478163, 0.00230115, -0.03638778, -0.01656794, -0.0446549, 0.02592212, 0.00160074, 0.02789506, 0.03598327, -0.01495559, 0.01205902, -0.01338804, 0.00764789, 0.00687598, -0.01134313, 0.00098048, -0.01612713, -0.04248057, 0.04146287, -0.06618343, 0.01954296, 0.05670697, 0.0049877, -0.00643055, -0.02405844, 0.05958952, -0.00496969, -0.00212876, 0.02421981, -0.00877622, 0.02078401, 0.06646374, 0.00463521, 0.02237115, -0.02571253, 0.01657574, 0.02379995, -0.03636821, -0.03903237, 0.0170177, -0.04528928, 0.03963055, 0.02147591, 0.05167189, 0.0361417, -0.01193351, 0.0045973, 0.0169985, -0.00251268, 0.06160134, 0.01974926, -0.00848916, 0.02688309, -0.02319925, 0.02508129, 0.00640139, -0.02685218, 0.00299356, -0.01624885, 0.03235181, 0.00557108, -0.00672753, -0.01320509, 0.02603445, 0.00090714, 0.02929826, -0.00131448, 0.01471489, -0.00740508, 1.647e-05, -0.00983773, -0.03204206, -0.00842744, 0.04801704, -0.05398622, -0.05027021, -0.01733407, 0.02443942, -0.04284275, 0.00912697, 0.00296125, -0.00928438, -0.01862012, 0.02983371, -0.05118451, 0.01665736, -0.02630095, -0.015148, -0.01622384, -0.00685793, -0.02443311, 0.00643248, -0.01340455, 0.01398352, -0.01254464, -0.03564167, 0.03683108, 0.03866352, 0.02170395, 0.07036532, -0.0585857, -0.02685785, 0.00570757, -0.04130864, 0.01088363, -0.02280666, -0.00843732, 0.02247394, -0.03115362, -0.0184617, 0.03276615, 0.00434122, 0.00118729, 0.01318039, 0.02775718, -0.00765413, 0.03615822, 0.0410177, -0.04027576, -0.02487185, 0.00861458, 0.00258741, 0.00957091, 0.02297444, -0.01899783, 0.01039637, -0.01200219, -0.02791018, -0.02545971, 0.041005, -0.0047675, -0.01681974, -0.02489981, -0.0424229, 0.01569933, 0.00251965, -0.03134727, -0.03811416, 0.04316403, 0.08187945, -0.02264643, 0.03186852, -0.00367425, 0.03763448, -0.00993224, -0.01794255, 0.00058375, -0.02623404, -0.02314843, 0.01635599, 0.01855206, -0.00762251, 0.04481785, -0.04419896, 0.04234679, -0.02328146, 0.00655706, 0.02831062, -0.02232897, -0.01519856, 0.01888389, -0.02988493, 0.012631, -0.01528024, -0.00082654, -0.00259822, 0.02560947, 0.02033865, 0.01900232, -0.00204305, 0.01150134, 0.01515707, -0.02596901, 0.01268829, 0.01790081, -0.03778924, -0.00543547, -0.00406061, -0.00949987, -0.04284322, 0.0324205, 0.01479596, -0.03274914, 0.05014386, 0.00890478, -0.00106113, -0.02828382, -0.00882702, -0.03444408, 0.01722394, -0.00625976, 0.0053767, -0.04429607, 0.02699438, 0.00617466, 0.00211816, -0.02014569, -0.01896112, -0.01784537, -0.02658656, 0.02078969, -0.02305043, 0.0165121, 0.02704461, 0.02422231, -0.01603201, 0.01546417, 0.00251087, -0.04412254, -0.00583356, 0.01148966, -0.01565, 0.00274655, 0.01586961, -0.00655684, -0.0134784, 0.01084646, 0.03893376, -0.0391886, 0.01052805, 0.0001272, 0.01986084, -0.04289405, -0.05457718, 0.05571133, 0.0097192, 0.01080706, 0.00240649, 0.00474797, -0.02898615, -0.02130191, -0.02114322, 0.00760534, -0.00963705, -0.02457996, -0.03480683, -0.00783165, 0.01097869, 0.0257023, -0.03015472, 0.01235893, 0.02861664, 0.02405587, 0.00969947, 0.01396557, 0.01813413, -0.02010017, -0.01238988, 0.02459531, -0.00340235, -0.00162006, 0.0135052, 0.00164895, -0.01099245, 0.02588719, -0.03252151, -0.03552965, -0.03041742, -0.00601046, -0.02446255, -0.00704618, -0.04789126, -4.954e-05, 0.00338439, 0.02432768, -0.02722115, 0.01529332, 0.00124441, -0.01653134, 0.02479267, 0.01431571, -0.01942647, 0.02419876, 0.02202723, -0.01665093, -0.02695072, 0.00222628, -0.00639498, 0.02014651, 0.02676339, 0.03956308, -0.01369319, 0.04704916, -0.03873541, 0.02437546, -0.00622367, 0.01940124, 0.03033005, -0.02194949, 0.01715, -0.02770716, 0.02841051, -0.00650047, 0.01866671, 0.01001811, -0.00035808, 0.02210505, 0.00517589, 0.006364, 0.02319599, -0.02613883, 0.052524, 0.0007413, -0.02502294, 0.0159968, -0.0092039, -0.04691819, 0.01042227, -0.01200925, -0.01496978, 0.01269568, -0.02699233, 0.04016836, 0.01333199, -0.05006592, 0.02454938, -0.00560796, -0.01546356, 0.03551092, -0.01213498, 0.02106512, 0.02996562, -0.00189607, 0.0161138, 0.00075976, -0.02556913, 0.00352146, 0.00928454, -0.00067567, 0.01581883, -0.01666232, 0.01163071, -0.00820246, 0.06099291, -0.0010282, 0.05056807, -0.0124066, -0.01037949, 0.05030742, 0.03524582, 0.01007185, -0.00907486, 0.00556389, 0.0192123, -0.00704237, -0.02862096, 0.02563817, -0.01585038, -0.00236671, 0.01259099, -0.00097625, 0.01324219, -0.00059443, -0.01299283, -0.013445, -0.03183869, 0.00517316, -0.02457287, -0.0252554, -0.00367537, -0.00504103, -0.01112341, -0.02669389, -0.01888707, 0.00443472, 0.00871126, -0.00132761, 0.00117373, -0.00651538, 0.02260626, -0.01668252, 0.02317832, 0.01307949, 0.02371611, 0.02501877, -0.02592102, -0.00871668, -0.0187432, -0.00853709, -0.03324315, -0.02110447, -0.01912956, 0.02612774, 0.05234624, 0.00213004, 0.03373011, 0.0142524, -0.01539061, -0.0045717, 0.01886944, 0.01964235, 0.00475052, 0.01772606, -0.00146649, 0.0019161, 0.01332261, 0.03088456, 0.00731671, 0.01178374, -0.03212655, 0.01685982, -0.01973662, 0.01819625, -0.00171813, -0.02394176, 0.00581246, -0.00889629, -0.02156374, 0.00932989, 0.03099763, 0.01775965, -9.384e-05, 0.00731418, 0.00663197, -0.00980974, 0.05328399, -0.01289309, -0.0186994, -0.00349834, -0.01790924, -0.00206105, 0.00810714, -0.01810191, 0.03452998, -0.0110951, -0.02953478, 0.02334492, 0.04951909, 0.01519035, -0.00462818, 0.01418903, 0.01800784, 0.00723093, -0.01886404, 0.02295573, -0.03795816, 0.01373681, 0.05209716, -0.01423241, -0.0053054, 0.01089218, 0.01529994, 0.01700805, -0.01185557, 0.01100603, -0.02570973, -0.05026144, -0.02212945, 0.02815022, 0.00303749, -0.0160162, -0.01565203, -0.04086837, -0.00174317, 0.01166821, 0.04458137, 0.00545632, 0.00098542, -0.00755393, 0.00606476, -0.02152711, 0.0174415, 0.02481752, -0.00152419, -0.00636568, -0.00864269, -0.02622424, 0.00036713, -0.04456993, -0.02013638, -0.00232461, -0.02213377, 0.00532506, 0.00177848, -0.01212576, 0.01054165, 0.04459931, -0.00336378, 0.0240177, 0.01711845, 0.01282796, -0.00771595, 0.01204654, -0.04555478, -0.00739093, 0.00675628, -0.01178981, 0.04112653, 0.0132744, 0.02017188, -0.02808718, 0.00772063, -0.00684127, -0.01658007, -0.04231962, -0.01038938, 0.03361335, 0.00022415, -0.00372118, 0.00227018, 0.04429545, -0.02747077, 0.01193169, -0.01267072, -0.00126674, -0.00864105, 0.03138031, 0.02324902, 0.05093015, -0.02225111, 0.00441205, 0.01178288, -0.00639696, -0.01509772, 0.01032722, 0.02082737, 0.00298747, 0.0074363, 0.00326787, -0.01219657, -0.01137482, -0.00210101, -0.00385824, -0.00567531, 0.0434126, -0.00160883, 0.01510658, 0.00907363, -0.00647597, -0.01983681, -0.02385187, 0.00443239, -0.00116718, 0.02030068, -0.01667946, -0.04044789, 0.00607526, 0.01000804, 0.01724847, -0.03690202, -0.04411919, 0.04104416, 0.00278661, -0.02544321, 0.03496965, 0.04200819, -0.03467696, 0.02222642, -0.02528588, 0.02120464, 0.01508165, -0.01784213, 0.03997355, 0.02833888, 0.02500892, 0.02872028, 0.01902327, 0.01288073, -0.02884419, -0.00012014, -0.00214759, -0.00500758, 0.04000057, 0.00319509, -0.00527902, 0.03915324, -0.0203705, -0.00053511, 0.01047915, 0.00475699, 0.01156198, 0.01026888, -0.00880824, -0.01894437, 0.00993361, 0.03040957, -0.00337696, 0.0135041, -0.02817503, 0.00547473, 0.01371503, -0.03161933, -0.03935035, -0.00955682, 0.03747815, 0.02844536, -0.04528766, 0.00038521, -0.01134713, -0.02071721, 0.01877219, -0.00738973, 0.01437925, 0.0162599, 0.00708768, 0.00286255, 0.02052543, -0.01044726, 0.02486398, 0.00861244, -0.02570807, -0.0083354, -0.00881964, -0.01914617, -0.00323651, 0.01767979, 0.02004168, 0.01622153, -0.01870952, 0.00717141, -0.03128545, -0.0110368, 0.01825486, -0.00102014]}, "type": "document"}, {"id": "713d47b5-96bc-4cf7-8ecb-ea015ed8a75f", "properties": {"page_content": "title: \"Speaker Series\" description: \"This page provides the videos and resources of past DIB Speaker Series events and an active list of upcoming DIB Speaker Series talks\"\n\nDiversity, Inclusion & Belonging Speaker Series\n\nThis is a series of Speakers, Q&As, Talk and Workshops that the Diversity, Inclusion and Belonging Team, the DIB Advisory Board or Team Member Resource Groups organise on topics of DIB. If you have an idea for a speaker please reach out to a DIB Team Member or submit a DIB Speaker Engagement Issue Template.\n\nUpcoming Speakers\n\nPast Speakers and Events\n\n26 of July 2022 - <PERSON><PERSON>\n\n{{< youtube \"lIFhCvCIfZY\" >}}\n\n24th of June 2022 - <PERSON><PERSON> Billingsley\n\n{{< youtube \"gjo4vAFzKqk\" >}}\n\n19th/ 20th of October 2021 - 50 Shades of Bias with <PERSON>, <PERSON> and <PERSON>\n\n{{< youtube \"4WCZqNVz-ck\" >}}\n\nJuly 19th - Neurodiversity in the Workplace with <PERSON>\n\n{{< youtube \"KJwIDoxQeSc\" >}}\n\n**June 17th Fireside and Q&A with [<PERSON>ojo<PERSON>]\n\n{{< youtube \"r2o2im4Gvog\" >}}\n\nMay 19th 2021 GitLab Fireside chat with <PERSON>trepreneur and CEO of Plateitforward\n\n{{< youtube \"4xhj__1cyG8\" >}}\n\nApril 15th 2021 GitLab x Kinspace: The importance and power of storytelling in building inclusive and intersectional workplaces**\n\nZoom chat box as a lot of interaction here\n\n{{< youtube \"hTeyU-z7z_c\" >}}\n\nMarch 8th 2021 International Women's Day Event: Kathryn Jacob, Sue Unerman and Mark Edwards. Authors of The Key to Transforming and Maintaining Diversity, Inclusion and Equality at Work\n\n{{< youtube \"YNwpQITr8VM\" >}}\n\nSeptember 24th 2021 Sekou Kaalund - JP Morgan Chase\n\n{{< youtube \"4n8vRfvVyGE\" >}}", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-speaker-series.md"}}, "type": "document"}, {"id": "f440cb9f-781e-43da-943a-5d3b53a720be", "properties": {"page_content": "title: \"Sales Sponsorship Pilot Program\" description: \"A page for the Sales Sponsorship Pilot Program\"\n\nIn 2023 we conducted a pilot program to address an imbalance of Black Team Members at job grade 9+ within Sales and lack of representation in sales leadership positions. The pilot program was designed to create pathways for Black Team Members to progress effectively at GitLab. We have since extended this program to promote pathways for all Sales team members to progress at GitLab.\n\nMentorship vs Sponsorship\n\nHarvard Business Review defines the difference between sponsors versus mentors: “While a mentor is someone who has knowledge and will share it with you, a sponsor is a person who has power and will use it for you.”\n\nMentoring is defined in our Mentoring at GitLab handbook page as: Mentor relationships are an opportunity for individuals to learn from someone's personal experience, background, and perspective. These relationships build trust on a team, provide safe space to make mistakes, and encourage both personal and professional development. Mentorship is an opportunity for both the mentor and mentee to develop their leadership and communication skills. Mentorship should be led by the mentee, similar to how 1:1's at GitLab are driven by direct reports.\n\nSponsorship is defined using influence and/or power to support the growth and development of a team member. A sponsor is typically a senior leader with significant influence and responsibilities. A sponsor will then use this influence to but not limited to; advocating for career advancement opportunities, provide opportunities to upskills via active projects and provide visibility to other senior leaders and the business of the sponsees potential.\n\nRole of a Sponsor at GitLab\n\nA Sponsor is someone who has power and influence at GitLab and will use that power to advocate, elevate and impact a team members opportunities and career progression at GitLab.\n\nA sponsor at GitLab is:\n\nA Senior Leader at a minimum job grade 10+ and is not the sponsees direct manager.\n\nThe senior leader should be a People Manager or Manager of Managers\n\nMust have been at GitLab for 6+ months\n\nBe able to commit to a 9 month sponsorship program which will include\n\nSponsor Training\n\nKick-off calls\n\nBi-weekly 1-1 with Sponsee\n\nBe able to fully commit and be held accountable for their role within the Sponsorship Program\n\nAs a Sponsor you should be:\n\nBe open and honest\n\nListen and Learn\n\nLean into discomfort in areas not familiar to you (e.g. cultural differences)\n\nTake risks with the relationship\n\nAdvocate for your sponsee with intent\n\nGive feedback as appropriate\n\nBe intentional in building the relationship\n\nA sponsor will:\n\nUse their influence and power to advocate for the sponsee potential for career advancement\n\nUse their influence to increase the visibility of the sponsee amongst other Senior Leaders at GitLab\n\nUse their influence to increase the visibility of the sponsee at GitLab\n\nProvide opportunities and space for risk-taking and growth\n\nProvide feedback on growth and skill development\n\nThe sponsorship relationship may not provide all the above but the sponsor should be willing and able to provide some of the opportunities above to the sponsor.\n\nRole of a Sponsee\n\nA Sponsee at GitLab will be (Pilot Program Only):\n\nBe performing in your role in accordance with your managers expectations, which take into account performance exercises such as 9 box and not on a Performance Improvement Plan\n\nBe a part of the underrepresented group the sponsorship program is targeted at.\n\nBe able to commit to 9 months within the program\n\nWilling to commit to the overall expectations of the program as outlined throughout the handbook page\n\nAs a Sponsee you should be:\n\nBe able to lead the relationship with your sponsor\n\nBe able Take risks with the relationship—trust is a vital part of the relationship\n\nBe able to do the work required based on feedback received and requirements for success\n\nBe able to request and give feedback as appropriate\n\nBe able to take personal responsibility for your career and be empowered to ask for what you need\n\nA Sponsee will:\n\nMaintain or exceed their performance record\n\nMaintain trust & confidentiality in the relationship\n\nAssist the sponsor in insights to the business and challenging perceived norms\n\nBe an ambassador for our CREDIT Values\n\nHave aspirational goals for career development; within leadership or as a senior IC\n\nHave a growth mindset and will be able to continually learn\n\nHave the ability to take on special projects which will impact their growth\n\nMakes themselves available for opportunities and stretch assignments\n\nConsistently performing at or above the performance bar\n\nRole of Sponsees Manager\n\nThe manager will support the team member in ensuring that have a Individual Growth Plan (IGP)\n\nAs well reassess the IGP at regular intervals during the sponsorship program\n\nThe manager will regularly check in on the progress of the Sponsorship Program with their report\n\nThe manager will regularly report on the success of the program to the leadership advocates of the program\n\nParticipate in a manager of sponsees session\n\nSponsorship Program\n\nThe program will last 6 months and the Sponsor & Sponsee will be the same person throughout the program.\n\nThe program will consist of:\n\nAn Individual Growth Plan is created in conjunction with your direct manager. The sponsor will add further suggestions throughout the program\n\n1-1 conversation in a cadence agreed upon by the participants.\n\nWe suggest a minimum of bi-weekly.\n\nSpecial projects & Sponsorship activities may mean cadence changes\n\nQuarterly All-Hands\n\nThis will reinforce the ideals of the sponsorship program and ensure that the relationship is progressing through the phases.\n\nIndividual Quarterly Feedback with DIB Team\n\nEnd of Program session with Sponsor\n\nComplete Next Step Documentation with Sponsor & Direct Manager to ensure progression continues\n\nSponsor Sponsee Matching:\n\nA sponsor should be at least two job grades higher than the sponsee\n\nA sponsor and sponsee should be in a timezone that is practical to have 1-1s. Special projects etc can follow asynchronous principles\n\nIf possible Sponsor should align with the career development plan of the sponsee\n\nWhat does a successful sponsorship look like?\n\nBuild\n\nTake the time to build a solid relationship with each other. This will be particularly important if you have no previous direct working relationship and can often take some time to cultivate. It is very important to build the relationship first before moving into authentic sponsorship.\n\nSuggested Actions:\n\nCommit to regular 1-1s\n\nUnderstand the sponsees career development plan\n\nSet goals and expectations early\n\nDevelop\n\nYou have taken the time to build a relationship with each other, the next step is to develop that relationship by becoming action & capability focussed. In this step the sponsor will help guide the sponsee on areas of improvement in skills & capabilities. The sponsee is responsible for acting on feedback and being intentional about displaying these improvements to the sponsor.\n\nSuggested Actions:\n\nFind and seek opportunities for the sponsor to observe the sponsees improvement areas\n\nSponsee invites Sponsor to a team meeting where they are presenting\n\nSponsor invites sponsee to a working group\n\nCommit\n\nThis is where both parties agree to move forward with the next step, which is sponsorship and advocating for the sponsee. This can take many forms, such as; a formal discussion, the sponsor outlining actions to the sponsee or at the sponsees request. This is an opportunity to provide feedback, any uncertainties and to reestablish career development goals.\n\nSuggested Actions:\n\nParticipate in feedback session with sponsee\n\nAdvocate\n\nNow that a commitment has been made and the sponsor is satisfied that the sponsee is ready for the next step. The sponsor actively and intentionally advocates for sponsees continued career development and advancement at GitLab.\n\nGoals & Benefits of Sponsorship Program\n\nGoals:\n\nThe goal of this program is to provide team members from underrepresented groups opportunities to have more visibility at GitLab. We are starting with the Black team member population in sales as they are very underrepresented in all areas of leadership. The goal is that programs like this will help redress the imbalance and ensure we are moving towards a more diverse, equitable and inclusive workplace.\n\nBenefits:\n\nSponsor:\n\nExposure to a diverse set of team members at GitLab, increasing their ability to lead diverse teams\n\nExposure to new ideas and be challenged on the status quo\n\nIntentional talent management & succession planning\n\nBetter understanding of the challenges of team members from underrepresented groups\n\nSponsee:\n\nIncreased exposure to GitLab and visibility to senior leaders at GitLab\n\nAccess to feedback from a senior leader\n\nAbility to develop skills and capabilities\n\nIncreased control over your career development plan\n\nGitLab:\n\nA more diverse team at leadership team\n\nIncreased retention of team members\n\nAttraction of underrepresented groups to GitLab\n\nTeam members more motivated to maintain performance levels\n\nMeasurables\n\nStart, Mid and End of the program - Satisfaction score from both Sponsor and Sponsee\n\nCareer Advancement Rate within twelve months of the program\n\nPilot Project Plan\n\nPhase 1:\n\nDevelop training materials for Sponsors & Sponsees => 0%\n\nDevelop program materials => 0%\n\nSponsorship 1-1 guide => 0%\n\nNext Steps Doc - for End of Program => 0%\n\nIdentify first cohort of sponsees amongst Team Members in Sales => 0%\n\nWhat criteria they have for a sponsor => 0%\n\nIdentify first cohort of sponsors with the Sales Org => 0%\n\nWhat expertise they are willing to provide and opportunities they can provide => 0%\n\nMatchmaking of Sponsors & Sponsee => 0%\n\nComplete => => 0%\n\nPhase 2:\n\nConduct Kick-Off call to present the program => 0%\n\nConduct Sponsorship Training => 0%\n\nRelease Sponsorship Matches => 0%\n\nConduct Pre-Start AMA => 0%\n\nPhase 3:\n\nBegin the Sponsorship Program => 0%\n\nQuarterly check in call - Group => 0%\n\nQuarterly feedback individual check in => 0%\n\nNext Steps Doc completion => 0%\n\nEnd Program => 0%\n\nPhase 4:\n\nRetrospective on Program Success/Improvements => 0%\n\nFinal feedback from Participants => 0%\n\nProvide iteration recommendations => 0%", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-sponsorship-program.md"}, "headlines": ["Mentorship vs Sponsorship", "Role of a Sponsor at GitLab", "Role of a Sponsee", "Sponsorship Program", "What does a successful sponsorship look like?"], "summary": "The Sales Sponsorship Pilot Program at GitLab aims to address the underrepresentation of Black team members in sales leadership by creating pathways for career progression. It distinguishes between mentorship, which involves knowledge sharing, and sponsorship, where senior leaders use their influence to advocate for a sponsee's advancement. Sponsors, typically senior leaders, commit to a 9-month program involving training, regular meetings, and providing opportunities for growth. Sponsees must perform well in their roles, belong to underrepresented groups, and commit to the program's expectations. Managers support sponsees through Individual Growth Plans and regular check-ins. The program includes phases of building, developing, committing, and advocating within the sponsorship relationship. Goals include increasing visibility and career advancement for underrepresented groups, while benefits encompass exposure to diverse perspectives, talent management, and improved retention. Measurables track satisfaction and career advancement rates. The pilot project plan outlines phases from material development to program retrospectives.", "summary_embedding": [0.03682643, -0.13325857, -0.03022319, -0.04326551, -0.06036484, 0.00418242, -0.0170405, 0.00556318, 0.04411002, -0.05121662, 0.12267882, 0.15616181, 0.02808133, 0.00564886, 0.05495496, 0.03675789, -0.11415884, 0.04516558, 0.04396836, -0.00205853, -0.04233732, -0.04967135, -0.05387381, 0.04195501, -0.02040359, -0.16103861, -0.00421292, -0.077549, 0.04021115, -0.00176687, 0.04103736, 0.04244353, -0.01135444, 0.14789174, 0.00236553, 0.01871396, -0.02972805, 0.02462264, -0.04288216, 0.03476502, 0.02644835, 0.06555209, -0.03497235, -0.06936428, 0.02741814, 0.11108527, 0.02736404, -0.04009819, 0.0607109, -0.00119506, 0.00498175, 0.0541643, 0.0511598, 0.02801389, -0.02096946, -0.02921588, -0.04044425, -0.0166401, -0.00966679, -0.05224065, -0.05998872, 0.03351361, -0.06620998, -0.07422574, 0.04260182, 0.03005551, 0.02991549, -0.00969776, 0.01579196, 0.00312088, -0.04479394, -0.03469209, -0.020539, 0.07795477, -0.03502626, -0.0060783, 0.00428046, 0.03358864, -0.03707677, 0.00714226, -0.03147671, 0.05152861, -0.01135339, 0.01232446, -0.00741811, 0.03345173, -0.07425793, -0.02117452, 0.03727315, 0.01930481, 0.10462502, -0.02706034, 0.007191, -0.02255226, -0.04215227, 0.02690955, 0.02775241, -0.00169717, 0.06647016, -0.03942648, -0.02420921, 0.0083273, -0.05485034, -0.09125923, 0.02066607, 0.09412497, 0.00936857, -0.0007974, 0.01223106, -0.0694102, -0.02793828, 0.01100961, 0.01788425, 0.00437113, -0.00984773, -0.03043713, 0.04953675, -0.04464767, -0.05108962, 0.00398868, 0.06750242, 0.06416675, -0.05055707, -0.05855792, 0.0339909, -0.05116793, 0.01455433, 0.02344432, 0.01076534, -0.05222617, -0.0256004, 0.02791465, 0.00845606, -0.0274985, 0.04015187, 0.04428393, 0.02965328, -0.01923331, 0.02175762, -0.01526511, -0.01451127, 0.00538924, 0.05724286, 0.02317965, 0.00435346, 0.03231146, -0.03150185, -0.01626003, 0.02007253, 0.02105947, 0.01215828, 0.04792341, -0.09419618, 0.00565812, -0.07323889, -0.00131179, 0.04087552, -0.00536841, 0.02199371, 0.00654809, -0.00411183, 0.04683619, 0.05868942, -0.00179874, -0.01971687, 0.02792871, 0.020793, -0.03787868, 0.07915652, -0.02082566, 0.01130999, 0.05856622, 0.03612715, 0.00273314, -0.02453536, -0.05784657, 0.02331877, 0.00287465, -0.01644323, 0.05629646, 0.00209948, -0.02678623, -0.02053082, -0.01093489, 0.02043788, 0.07910641, 0.0290948, 0.00627574, -0.04689907, 0.02167941, -0.05454216, -0.03980964, -0.00788069, 0.02785515, 0.00832798, -0.01212946, -0.10021506, -0.00494779, -0.02577466, 0.02522443, 0.02924856, -0.02287435, 0.01700496, -0.057284, 0.01233338, 0.07854309, 0.05535867, 0.03641478, 0.01475128, 0.05501598, 0.00282395, 0.03164221, 0.07669879, -1.419e-05, 0.01801051, 0.03676842, -0.01025569, -0.02393788, -0.00658696, -0.07982966, -0.04838814, -0.00608923, 0.00012433, -0.03570791, -0.02278501, -0.04568744, 0.06344104, 0.02217673, -0.0261032, -0.00647638, -0.07016595, -0.04692431, 0.02648011, 0.01199064, 0.02103008, 0.01267111, 0.01060784, -0.05077554, 0.04080416, 0.03287842, -0.04125896, -0.04716367, 0.02233259, -0.05734535, -0.00739399, -0.00566528, -0.02312702, 0.004535, 0.02657306, 0.02090693, 0.02605875, -0.0401809, -0.02347261, -0.02579876, -0.07159051, 0.02141869, 0.03444782, -0.01023741, -0.04918518, 0.04660768, 0.03212739, -0.05603716, 0.01743753, 0.00638783, 0.07391144, 0.02376607, 0.03641589, -0.0150284, -0.0231734, 0.00852024, 0.00289156, -0.00144575, -0.05447245, 0.00153463, -0.00497557, -0.01675122, -0.01101699, -0.04634415, 0.05527889, -0.03107253, -0.05020037, 0.02012075, -0.01851224, 0.02068038, 0.00835265, 0.00775139, 0.04351903, -0.0162284, -0.01229691, -0.00305901, 0.02983711, -0.00384093, 0.01237105, 0.00949278, -0.01813522, 0.00451327, -0.02279327, -0.0028197, 0.00515339, -0.02738459, -0.02065784, -1.595e-05, -0.00996882, -0.02573407, 0.02711008, -0.02738251, -0.01376674, -0.038916, -0.0213027, 0.01866993, 0.04164554, -0.05172501, -0.00460678, 0.02580483, -0.0351261, -0.00806561, -0.00482047, -0.01590796, -0.04558689, -0.06051514, 0.02532602, 0.00875627, -0.00093077, -0.02657868, 0.0245008, -0.01087227, 0.01852477, -0.0103676, 0.00465849, 0.01095549, 0.04412524, 0.01754572, -0.02899119, 0.00496936, -0.02591921, -0.01681127, -0.04237833, -0.02626203, 0.04405805, 0.00380565, -0.0287507, -0.02819436, 0.01006785, -0.02242997, 0.01468807, -0.00328647, 0.05304718, -0.05981483, 0.0408509, -0.02588375, -0.03774796, 0.00259314, -0.04240106, 0.00263981, 0.00696292, 0.04756433, -0.0166742, -0.01859232, -0.01668536, 0.02733936, -0.01601649, 0.00515521, -0.04036003, 0.04059519, -0.01979394, -0.02451052, 0.02629127, 0.04812448, -0.03429411, 0.05076035, -0.01074901, 0.06345073, -0.00708588, 0.03938121, -0.03444589, -0.00239004, -0.01188097, 0.00695996, -0.00958273, -0.02444628, -0.00420662, 0.07888113, 0.02369814, -0.00372026, 0.03295536, -0.00926447, 0.04154308, -0.0534203, 0.0258267, -0.04221452, -0.00926549, 0.01762602, -0.07528496, -0.03030371, -0.03019641, -0.03586905, -0.0199754, -0.03666832, -0.00293929, 0.08666032, 0.01758249, -0.00864925, 0.00465165, -0.00123301, 0.04109114, -0.00976648, -0.03846591, -0.00051378, 0.00354929, -0.0093756, 0.00921174, -0.02321287, -0.01931591, 0.01868873, 0.01014132, 0.01988909, 0.00586689, 0.00936184, 0.01922252, -0.04511329, -0.0384845, 0.01127238, 0.02915151, 0.00996428, 0.00216196, -0.00061481, -0.01378646, -0.01882927, 0.00304152, 0.00645241, 0.05586423, 0.01851242, 0.050668, -0.04465345, -0.02212472, 0.0035406, 0.01443662, -0.01088564, -0.00348047, 0.0165158, 0.04039211, 0.02030314, 0.01801695, 0.02210754, -0.05565745, 0.0022493, 0.00396825, -0.00731188, 0.06035246, -0.04820814, 0.01798138, -0.01813468, 0.00862013, -0.03329583, -0.00526931, -0.0253843, -0.01306427, -0.00183148, -0.00851847, 0.01820093, 0.02650844, 0.00806626, 0.02071977, 0.00944581, -0.01962368, 0.00706444, 0.05986464, 0.01965287, -0.00697845, 0.02337208, 0.0005217, -0.04046547, -0.05688158, -0.0098136, 0.00245066, 0.05003338, -0.01878567, 0.05716624, 0.02633553, -0.02583787, 0.01631676, -0.00576977, 0.00623019, 0.01258326, -0.01524206, -0.02258243, 0.04239678, 0.02477281, 0.00121141, -0.01481632, -0.00147268, -0.04574568, -0.03835342, 0.02439715, -0.02264236, 0.00626132, -0.02095732, 0.02400461, 0.00977972, 0.00177872, 0.01926418, 0.03178711, 0.0059849, -0.02658631, -0.01484697, -0.00649715, 0.06315987, 0.04056004, -0.03216128, 0.05374035, -0.01119715, -0.0467645, -0.01925172, -0.04860305, -0.00038065, 0.00584824, 0.01126209, 0.01071574, 0.01594532, -0.00722554, -0.01922602, -0.00688986, 0.01126695, 0.0117263, 0.0465238, 0.01551377, 0.01338006, -0.03905949, -0.01223877, -0.00060205, -0.00413302, 0.04374707, -0.02156687, -0.0346249, -0.01627448, 0.02051115, 0.00543891, -0.05408029, -0.02058943, 0.03098967, 0.00552839, 0.00540254, -0.00744155, -0.00728788, -0.0087302, 0.0161178, 0.02724715, -0.01506946, 0.0629332, 0.01447353, -0.00316438, 0.02511694, -0.0429833, 0.01506183, -0.02448314, 0.00366197, 0.00115954, 0.01149923, -0.02298689, 0.02505662, -0.0119659, 0.01787735, -0.00589321, -0.01070188, -0.01587328, -0.04857792, 0.00772642, 0.02443064, -0.0809048, 0.02175831, 0.01767605, 0.00123056, 0.02955317, -0.01831091, -0.01248418, 0.01654411, 0.00204951, -0.02314976, -0.02976761, 0.01190567, 0.01573812, -0.02316498, -0.00443117, -0.02201662, 0.00132826, 0.00054626, -0.01135083, 0.00367652, -0.00936757, -0.04838938, -0.00776368, -0.00357857, -0.03530362, 0.04855816, -0.00374619, -0.02404594, -0.02863837, -0.07571325, -0.03254033, 0.0175585, 0.0296792, 0.01713527, -0.00687004, -0.04413711, -0.01176615, 0.02668389, -0.00054576, -0.05097685, 0.00442212, -0.02569881, -0.00469471, -0.00630397, 0.01158863, 0.03917384, 0.01648486, -0.02810279, 0.0025931, -0.01505826, -0.02123721, -0.00863085, 0.03532273, -0.02917011, 0.04722234, -0.01788205, 0.02926158, -0.01329769, -0.02091547, 0.02769255, -0.04275659, -0.02462765, -0.03038485, -0.00530762, -0.01900285, 0.01239852, -0.01612914, -0.0293607, -7.875e-05, -0.01089839, -0.00819532, -0.01111418, -0.02803102, -0.01752977, -0.02459026, -0.01046191, -0.0186123, -0.04008976, 0.08002747, 0.01301239, 0.00886626, -0.02837099, 0.02267917, 0.02369968, 0.02476019, 0.03481123, 0.01623048, 0.01352225, -0.03284618, 0.02875374, -0.03347485, -0.02522674, 0.05131453, -0.01237691, 0.00071696, 0.02286454, -0.01931199, -0.02433163, -0.01588305, -0.02971043, -0.01457676, -0.00175142, 0.02369748, -0.00390742, -0.00151553, 0.0308195, 0.03443159, -0.04807409, -0.01408022, 0.01064717, -0.0033946, 0.00705874, -0.01666201, -0.04202526, 0.01678816, 0.03324684, 0.01525934, -0.03460702, 0.00834969, 0.00879769, -0.00222667, 0.01719566, 0.00950594, 0.04619911, 0.01221714, 0.00043281, -0.00178394, 0.03899181, -0.00261009, 0.02480057, -0.02246984, -0.0209516, -0.01206533, 0.00083908, -0.01899807, 0.04175314, -0.0176651, 0.00431407, -0.00256623, -0.0351808, 0.0122104, 0.0105561, -0.04122859, 0.02251565, 0.01350651, 0.00087785, 0.03493902, 0.04327793, 0.00922972, -0.00349495, -0.00155143, 0.02559017, 0.00126668, -0.02858858, 0.04341309, 0.03323592, -0.05021711, -0.00373611, 0.02972457, 0.02306193, 0.01052971, 0.0032876, 0.04722812, -0.05488317, -0.02763465, 0.01644532, 0.01147984, -0.02800144, 0.03541147, 0.01047613, -0.01699396, -0.01310057, 0.01271944, 0.01536816, -0.00385089, 0.02964319, -0.01725644, 0.01460879, -0.00379348, 0.00528496, 0.06266892, 0.01951805, -0.01729517, 0.00855267, -0.03535684, -0.00623103, 0.02281595, 0.00732447, 0.01591413, -0.04846966, -0.01865015, 0.0166794, 0.03222151, 0.00085803, -0.00910699, 0.00865909, 0.00538999, -0.02619566, -0.02032198, -0.0041789, 0.02606028, 0.00499241, 0.01828323, -0.00919086, 0.01226671, 0.00262171, 0.05519808, 0.04076781, 0.00736147, -0.01063306, -0.00788096, 0.00877756, -0.02344519, -0.01366868, 0.02834038, 0.01714056, 0.01262466, -0.02528754, 0.01504904, -0.0397453, -0.01333446, -0.0327888, 0.00697469, -0.00033772, -0.00551068, 0.00311309, -0.0047089, 0.00597182, -0.00273938, -0.01630051, -0.00244104, 0.02169618, 0.00180933, -0.02346274, 0.00506447, -0.00983756, 0.04317063, 0.0173232, 0.03429835, 0.02733308, 0.02524706, 0.00078232, -0.00898257, -0.02601277, -0.01002854, 0.02323713, 0.01222289, 0.00751524, 0.00799841, 0.02049846, -0.00308074, 0.03413595, 0.05109362, 0.00766459, -0.01590182, -0.02251168, 0.0143208, 0.02771068, 0.00530924, 0.00509803, -0.02287196, -0.01074729, 0.0435563, 0.00049443, 0.02915805, 0.0126683, 0.00760562, -0.03926014, -0.01810323, 0.01193064, 0.01178716, 0.03904469, 0.00168612, 0.05361373, -0.01090462, 0.00843294, -0.01422873, -0.01845137, 0.00587904, 0.05236156, -0.0340295, -0.00724926, 0.01714699, 0.02200009, 0.0239452, -0.01529465, -0.01812809, -0.02936178, -0.06742951, -0.05541178, 0.02118274, -0.00718395, -0.0048156, -0.03390121, -0.01857094, 0.00999786, -0.01164892, 0.02005813, -0.04273795, 0.01110542, -0.04780543, -0.01067433, 0.04654107, 0.03392977, 0.02359343, -0.00704881, -0.00739881, -0.05098049, -0.02478852, -0.00188298, 0.0052145, -0.02606109, -0.02628134, -0.00089208, -0.01511626, -0.02747775, -0.023425, 0.03267488, 0.01472621, -0.05772097, 0.01639123, 0.04826565, -0.0152302, -0.01353973, 0.01170355, -0.03350627, 0.00260342, 0.01955075, 0.00988093, 0.01483938, 0.01468862, -0.01677312, -0.0003576, 0.01300563, -0.00110902, -0.0044346, -0.02150876, 0.01850053, 0.01225974, -0.00052542, 6.872e-05, -0.00110157, 0.01466777, 0.00083546, -0.01749236, 0.00670473, 0.00222856, -0.00823067, 0.01956351, 0.01593304, 0.01399584, -0.00803497, 0.0322503, 0.01990592, 0.01994781, 0.02003974, 0.00212879, 0.00427401, -0.00167593, -0.02284959, -0.00833328, -0.01990891, 0.02687778, 0.00511217, -0.00558929, -0.03858536, 0.01288476, -0.01783751, 0.01270251, 0.01491746, 0.01260658, -0.00787241, -0.0098991, -0.0400066, -0.00096302, -0.00926439, -0.01984947, 0.01010612, 0.03641798, 0.00857925, 0.00231435, 0.00229376, -0.05423337, 0.02248902, -0.01504869, 0.00329925, -0.02401439, -0.01880179, -0.00912076, 0.01258284, 0.01872745, -0.01766757, 0.01358999, -0.00604566, 0.00263002, -7.31e-06, 0.03408183, 0.01317158, 0.01294991, 0.00810956, 0.00563738, -0.02627422, 0.01208507, -0.03228104, -0.00056318, -0.01578252, 0.01396152, 0.01789379, -0.01527821, -0.02043854, 0.04390754, -0.00127031, 0.04657718, -0.00806696, 0.02873446, 0.00050812, -0.00120408, 0.0049714, 0.00575318, -0.00433362, -0.00496138, 0.01376173, 0.00784361, -0.01466483, -0.0361471, -0.01069607, -0.00460509, 0.00395393, -0.01047525, 0.03438705, -0.01852477, -0.0086281, 0.00232558, -0.02769173, -0.0010276, 0.00861723, 0.01855477, -0.021737, -0.00179355, 0.02819338, 0.00096248, 0.02424326, -0.02877307, -0.00773544, 0.0211095, -0.00879068, 0.04620297, 0.01439507, 0.05255896, 0.0099866, -0.00329854, -0.01878737, -0.02022478, -0.00270203, 0.00453936, -0.01625472]}, "type": "document"}, {"id": "cfd346b1-a03d-4b76-b981-dd9123f28421", "properties": {"page_content": "license: apache-2.0", "document_metadata": {"source": "Sample_Docs_Markdown\\README.md"}}, "type": "document"}, {"id": "e489e791-3f7d-4034-9680-9e6cc40813c3", "properties": {"page_content": "title: \"The Ally Lab\" description: Learn what is an ally, how to be an ally and what it means to be an ally. What is an ally? A diversity, inclusion and belonging \"ally\" is someone who is willing to take action in support of another person, in order to remove barriers that impede that person from contributing their skills and talents in the workplace or community. Being an ally is a verb, this means that you proactively and purposefully take action and is not something forced upon you. How to be an ally It is not required to be an ally to work at GitLab. At GitLab it is required to be inclusive. Being an ally goes a step beyond being inclusive to taking action to support marginalized groups. The first step in being an ally is self-educating. This ally lab will provide you with some of the tools, resources and learning activities to help you grow as an ally.", "themes": ["Ally", "Diversity", "Inclusion", "Belonging", "Marginalized groups", "Self-educating", "Action", "Support", "Workplace", "Community"], "entities": ["The Ally Lab", "GitLab"]}, "type": "chunk"}, {"id": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "properties": {"page_content": "Skills and Behaviors of allies To be an effective ally it is important to understand some of the skills and behaviors great allies exhibit. Active listening Neutral and nonjudgmental. Patient (periods of silence are not \"filled\") Verbal and nonverbal feedback to show signs of listening (e.g., smiling, eye contact, leaning in, mirroring) Asking questions. Reflecting back what is said. Asking for clarification. Summarizing. Empathy & Emotional Intelligence An example of this could be: A colleague comes to you and tells you that their pronouns are being misused often at work and it is making them feel uncomfortable and they are avoiding social calls and interactions. Whilst you haven’t experienced this yourself and unlikely you would experience this, you allow yourself to think of situations where you have felt uncomfortable at work before. You also put yourself consciously into the shoes of your colleague and think of a way you can practically help. You offer to your colleague that in the next 5 calls they participate in you will be on the call and actively point out misuse of their pronouns to other colleagues to take away some of the emotional burden. Active learning about other experiences You go beyond performative actions for example black squares on Instagram for Black Lives Matter, but actively does the work to understand the pain, struggle and experience of those burdened. This could look like: You are managing black team members, an incident has occurred externally that could affect the mental health of those team members. You actively research the experience and historical context of the trauma associated with the incident. You use this to ensure you are informed and able to appropriately apply empathy if a team member approaches you to ask for assistance. Humility Non-defensive Willingness to take on feedback You aren’t going to get it right all the time and you have to be ok with that. Be willing to take feedback on and not let it deter you from continuing to be an ally. Example of this could be: You are in a safe space with an underrepresented group acting as an ally and absorbing information. A point comes up that you are passionate about and you talk over someone in the group and take over the conversation. After the meeting someone from the group jumps on a Zoom meeting with you and explains that it felt you took away the viewpoints of a number of people from the URG because you took over the conversation and interrupted an individual. You apologize, take on the feedback, ask for any tips on how to make sure it doesn’t happen again and take the necessary steps. --- One of the mistakes that often happens here is being defensive or justifying the action. The group will already know you are operating with good intent but generally are wanting to help you level up in their lived experience. Courage Comfortable getting uncomfortable Speak up where others don't or can't The empathy example is also a good example of this. Self-awareness Own and use your privilege This could look like: You are in a product meeting and the meeting will be making critical decisions about the product roadmap for the next three months and you notice that everyone in the meeting is of the same gender and race. You use your privileged situation in the meeting to point this out and ask the people in the meeting. Who should be invited to ensure we are getting a diverse perspective and viewpoint on the agenda items for the meeting? Action orientated You see something, you say something The example above is a good example of this: Ensure decisions and conversations have diverse voices. I.E. you are in a meeting and everyone looks the same, insist on other perspectives. In a group setting when a discussion or comment is verbalized that could be controversial use language similar to this to course correct the conversation: \"I would like us all to be aware of our language and/or acknowledgements and ensure we are being respectful to all individuals. Thank you.\" \"I am practicing being an ally and as a reminder I would like to ensure we are all using inclusive language\"", "themes": ["Skills and Behaviors of Allies", "Active Listening", "Empathy & Emotional Intelligence", "Active Learning", "Humility", "Courage", "Self-awareness", "Privilege", "Action Orientated", "Inclusive Language"], "entities": ["<PERSON><PERSON>", "Tesla", "SpaceX", "Europe", "Asia", "Berlin", "Shanghai", "Black Lives Matter", "Instagram", "URG"]}, "type": "chunk"}, {"id": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "properties": {"page_content": "Concepts & Terms Privilege: an unearned advantage given to some people but not all Oppression: systemic inequality present throughout society that benefits people with more privilege and is a disadvantage to those with fewer privileges Ally: a member of a social group that has some privilege, that is working to end oppression and understands their own privilege Power: The ability to control circumstances or access to resources and/or privileges Marginalized groups: a person or group that are treated as unimportant, insignificant or of lower status. In a workplace setting, employees could be treated as invisible, as if they aren't there or their skills or talents are unwelcome or unnecessary Performative allyship: referring to allyship that is done to increase a person's social capital rather than because of a person's devotion to a cause. For example some people used #metoo during the Me Too movement, without actually bringing more awareness or trying to effect change. Tips on being an ally Identifying your power and privilege helps you act as an ally more effectively Follow and support those as much as possible from marginalized groups When you make a mistake, apologize, correct yourself, and move on Allies spend time educating themselves and use the knowledge to help Allies take the time to think and analyze the situation Allies try to understand Perception vs. Reality Allies don’t stop with their power they also leverage others powers of authority See our Ally Resources Page for more resources on being an ally. Allyship & Empathy Being an Ally Requires Empathy {{< youtube \"1Evwgu369Jw\" >}} Responding with Empathy What to say, when you don’t know what to say: Acknowledgement of their pain. “I’m sorry you are going through this.” “That must be hard.” Share how you feel. “Wow. I don’t know what to say.” “It makes me really sad to hear this happened.” Show Gratitude that the person opened up. “Thank you for sharing with me.” “This must be hard to talk about. Thanks for opening up to me.” Show Interest. “How are you feeling about everything?” “Is there anything else you want to share?” Be Encouraging. “You are brave / strong / talented.” “You matter.” Be Supportive. “I’m here for you.” “I’m happy to listen any time.” Boot and Sandal Methophor Imagine you are wearing a heavy boot (represents privilege) and you are stepping on someone’s foot that is only wearing sandals (represents oppression). If someone says, “Ouch, you are stepping on my toes!” How do you react? Problems with common responses to mistakes become obvious: Centering the mistake around yourself: “I can’t believe you think I’m a toe-stepper! I’m a good person!” Denial that others’ experiences are different from your own: “I don’t mind when people step on my toes.” Derailing: “Some people don’t even have toes, why aren’t we talking about them instead?” Refusal to center the impacted: “All toes matter!” Tone policing: “I’d move my foot if you’d ask me more nicely.” Denial that the problem is fixable: “Toes getting stepped on is a fact of life. You’ll be better off when you accept that.” Victim blaming: “You shouldn’t have been walking around people with boots!” Withdrawing: “I thought you wanted my help, but I guess not. I’ll just go home.” Instead, you would respond with the following: Center the impacted: “Are you okay?” Listen to their response and learn. Apologize for the impact, even though you didn’t intend it: “I’m sorry!” Stop the instance: move your foot Stop the pattern: be careful where you step in the future. When it comes to oppression, we want to actually change the “footwear” to get rid of privilege and oppression (sneakers for all!), but metaphors can only stretch so far! Improve allyship skills by following the three C's Consciousness Courage Compassion How diversity shows up on teams Image Areas you can show allyship Recruiting & Hiring Sourcing Interviewing Compensation Guidance & Support Helping through challenges Listening and empathizing Providing perspective Mentorship Difficult Conversations Performance conversations Ongoing feedback Growth & Career Development Career planning Skill development Mentorship and sponsorship Examples of Allyship at GitLab TBC Ally Lab Learning Group {{< youtube \"dPywm-j1iic\" >}} The Ally Lab Learning Group is an initiative to learn to become or be a better ally within a collaborative group of peers who are seeking the same aim of allyship growth. All team members have the ability to be an ally whether you are a part of an underrepresented group (URG) or not, there are URGs that may not belong to and you have the ability to be an ally to them. There will be regular intakes of team members into groups. The next intake: You can sign up here To sign up: TBC The Group: ALLG Facilitator 5-10 team members Diversity within the group where self-identification is available What you will need: The ability to have 1 synchronous 30-45min a week (Timezone convenient location) A higher intent to become an ally What you will do: Session 1: Async ally training and courses. Synchronous meeting to discuss what you have learned. Equality Ally Strategies Cultivate Equality at Work Communicating with Empathy Effective Listening Building Trust The importance of trust How to engage meaningfully in allyship and anti-racism - OPTIONAL Inclusive mindset of committed allies - OPTIONAL Session 2: The importance of being an ally and why you want to be an ally. Session 3: Work as a group to discuss a number of scenarios and how to tackle them as an ally. Session 4: Together, write a commitment to allyship and the values you will abide by to be an ally. In the last session you will decide as a group a short commitment statement that should be shared on the Diversity, Inclusion & Belonging Sharing Page. Think about what you have individually and collectively learnt during the experience. Create 2-5 values that, as a group, you will hold in your continuous learning in allyship and take into account when situations arise. Once completed you now have a safe group to", "themes": ["Privilege", "Oppression", "Allyship", "Power", "Marginalized groups", "Performative allyship", "Empathy", "Diversity", "Inclusion", "Anti-racism"], "entities": ["<PERSON><PERSON>", "Tesla", "SpaceX", "Europe", "Asia", "Berlin", "Shanghai", "Me Too", "GitLab", "ALLG"]}, "type": "chunk"}, {"id": "********-fd84-453a-a35e-08efcfc73dd9", "properties": {"page_content": "What it means to be an ally Take on the struggle as your own Stand up, even when you feel uncomfortable Transfer the benefits of your privilege to those who lack it Acknowledge that while you, too, feel pain, the conversation is not about you discuss allyship with, either to get advice, hold yourself accountable to or run through a situation. Things you can do post the 4 week ALLG: Schedule a quarterly or bi-yearly call with your group Ask members of your group for regular coffee chats to discuss the above Reflect regularly on your commitment and values you agreed to and situations where you could have displayed allyship Ally Training We held a 50 minute Live Learning Ally Training on 2020-01-28. The recording follows along with the slide deck and agenda. {{< youtube \"wwZeFjDc4zE\" >}} External Ally Training There are some essential skills that are required to be an ally, here are a number of trainings that will help you enhance your allyship skills. Some of these are not allyship specific but will sharpen your skills in those important areas. Equality Ally Strategies Champion Workplace Equality Effective Listening How to engage meaningfully in Allyship Becoming a true ally Building Trust Why trust matters Ally Learning Activity and Scenarios GitLab Diversity, Inclusion & Belonging resources Allies familiarize themselves with GitLab's general DIB content Diversity, Inclusion & Belonging page Gender and Sexual Orientation Identity Definitions and FAQ DIB training resources Unconscious bias Ally Resources Here are additional resources on being an ally Guide to allyship 5 Tips For Being An Ally Ally skills workshop. Check out the materials section with a handout PDF (linking to many more resources), slides PDF, videos, and more. Why cisgender allies should put pronouns on their name tag", "entities": ["<PERSON><PERSON>", "Tesla", "SpaceX", "Europe", "Asia", "Berlin", "Shanghai", "GitLab", "DIB", "YouTube"], "themes": ["Allyship", "Privilege", "Accountability", "Equality", "Diversity", "Inclusion", "Unconscious <PERSON>", "Effective Listening", "Trust Building", "Training Resources"]}, "type": "chunk"}, {"id": "f06c35f4-a311-4720-bb05-71268d20ef2c", "properties": {"page_content": "title: \"Building an Inclusive Remote Culture\" description: \"We are passionate about all remote working and enabling an inclusive work environment.\" We are passionate about all remote working and enabling an inclusive work environment. There isn't one big activity we can take to accomplish this. Instead, it is a mix of numerous activities and behaviors combined to enable our team members to feel they belong in GitLab. Those activities and behaviors include: DIB Events DIB Advisory Group Diversity, Inclusion & Belonging TMRGs - Team Member Resource Groups Our Values Family and Friends first, Work second Inclusive language and pronouns Parental leave Asynchronous communication and workflows Tips for Companies Defining Diversity, Inclusion & Belonging A great place to begin is to set a foundation of the basic understanding of how your company defines these terms. An example is GitLab's Diversity, Inclusion & Belonging value, supported by an evolving list of operating principles. In other places, you may hear them used interchangeably. Understanding their differences is essential to driving the initiatives. As an example, you may hire many diverse candidates, but if you don't create an inclusive environment the work can be in vain. Evaluating the company's current DIB landscape Consider what you are already doing in this space. What is the current feedback? What are team members saying with company engagement surveys? What are the goals you are wanting to achieve? What are the metrics saying? Diversity, Inclusion & Belonging survey A company survey is a great way to get a sense of team members' thoughts and concerns. The DIB team at GitLab runs an annual survey via CultureAmp to gauge where we are as a company in terms of diversity, inclusion & belonging. We take the results and implement projects and solutions based on insights from the survey results. Naming this body of work Although Diversity, Inclusion & Belonging are often understood globally, there are other terms that can be leveraged to name your efforts. The naming should be unique to your company. Examples could include Belonging, Inclusion & Collaborations, etc. Developing a mission statement When creating a diverse and inclusive culture, most companies will develop a mission statement to support their vision. Your mission statement should articulate the purpose of your strategy. In a few sentences, you should be able to succinctly provide the why and the how. Be sure to take into account your company’s current overall mission and vision. It is best to align your DIB mission and vision with your organization’s overarching mission and vision. To do this, you may consider how your DIB Strategy can build on, scale, or enhance the organization’s mission and vision. Creating TMRGs In general, TMRGs are an excellent support system and key to providing awareness, respect, and building diversity, inclusion & belonging within the workplace. These groups are a proven way to increase cultural competency, retention of team members, provide marketplace insights to the business, attract diverse talent, and more. The endorsement of TMRGs allows team members to identify common interests and decide how they can be shared with others. When creating TMRGs there are a few initial steps to consider: Creating guidelines to help support TMRGs being stood up within your company Naming of each TMRG Roles within each TMRG Aligning to company strategy Creating forms, Google groups, ways of tracking attendance for TMRG events, and membership metrics Creating a Diversity, Inclusion & Belonging Advisory Group Consider creating this group as your highest level of global voices: a team of company influencers who can be instrumental in driving DIB efforts from a global perspective. How do you do this? GitLab conducted a global \"All Call\" for those who would be interested in joining and advised them to provide \"why\" DIB was important to them, along with other questions such as division, location, etc. When we were reviewing we were able to have the best possible outcome of representation across the globe. Additional support in sustaining the group would be: DIB Advisory group guidelines Appointing an Executive sponsor from the company Designating leads of the group Deciding on when it is time to enact or rotate the opportunity for new advisory group members More to come DIB Initiatives DIB Awards for Recognition DIB Surveys DIB Framework Inclusive Benefits", "entities": ["GitLab", "DIB", "CultureAmp", "TMRGs", "Diversity", "Inclusion", "Belonging", "Team Member Resource Groups", "All Call", "Executive"], "themes": ["Inclusive Remote Culture", "Remote Working", "Diversity", "Inclusion", "Belonging", "Team Member Resource Groups (TMRGs)", "Asynchronous Communication", "DIB Advisory Group", "Mission Statement", "Cultural Competency"]}, "type": "chunk"}, {"id": "ae64034c-c2a1-4491-9785-79aacd8aa7f1", "properties": {"page_content": "Tips for Managers Set aside time to show up for planned DIB events You may be surprised by how much seeing your face in these events validates this as a worthwhile use of time which is valued by the company. Seeing you in attendance also opens the door to future conversations that might begin with: \"I saw you in XYZ meeting, what did you think of such-and-such topic?\" Incorporate DIB into your team meetings Connecting to team members is key to understand who they are and what they bring to the team. A great initial step is to start with open discussions. You could start by opening your next team meeting to chat about what they feel inclusion looks like, what is important to them as a team to feel included, etc. This could then move into monthly or quarterly team DIB icebreakers, trainings, etc. The idea is to make sure DIB is not touched on once and never mentioned again, but more of an understood aspect of your team environment. You can do this by setting and communicating DIB goals for your team, and sharing how we will measure success (an idea could be sharing DIB survey data with team members). Encourage Diversity of Thought and Speaking Up Don’t assume you know more than others. Give people a chance to add their perspective or expertise. Teach people how to disagree, set the expectation that it is OK to disagree, and encourage people to do it day-to-day. Celebrate diverse thoughts and thank people for making contributions, particularly if they disagree with you. It takes courage to disagree publicly with a leader. Connect with your team members To help team members feel comfortable being themselves, leaders should consider authentic ways to connect to their team members. Everyone wants to feel visible and included in order to perform their best work. Being visible usually includes being seen for accomplishments, acknowledgment of contributions, what is the same, and what differs from team member to team member. Leaders and all team members should show an interest in and respect for differences and contributions. Considerations could be that of language/terminology such as “spouses” or “partners” instead of making assumptions about team members sexual orientation. Being considerate of dietary restrictions when choosing food options for Contribute or other local gatherings. Acknowledging birthdays, recalling personal things that might have been mentioned in past calls such as moving to a new home, an ill family member or team member, and following up authentically. For more information see our Inclusive Language. Ask employees what pronouns they use Pronouns are a large piece of a person's identity and are often used to communicate a person’s gender, which is why it is so important to get it right. Asking for a person's pronouns and using those pronouns consistently shows that you respect their identity, but it also helps to create a more welcoming, safe, and supportive environment where people can feel comfortable being themselves. This is a change that goes a long way to foster inclusion. Be mindful of the times of your meetings It's important to consider global times and alternating meetings to accommodate regions. Be mindful of families, commitments, observances, holidays, and meeting times that may be out of team members' working hours. Every meeting time won't be perfect for everyone (which is why all meetings have an agenda), but making a conscious effort to alternate times is to ensure the same people aren't being excluded. For more, view our Inclusive Meetings operating principle. Be a Role Model As a manager, you are in a unique position to model good DIB practices for the rest of your team. Be authentic, own up to mistakes, and commit to doing better next time. Don’t dwell on it. Set a good example by admitting that you make mistakes and invite people to help you and each other by speaking up in accordance with our feedback guidelines. By holding each other accountable, we get better as a team.", "entities": ["Managers", "DIB", "XYZ", "Inclusive Language", "Inclusive Meetings"], "themes": ["Diversity, Inclusion, and Belonging (DIB)", "Leadership engagement in DIB events", "Incorporating DIB into team meetings", "Encouraging diversity of thought and open discussions", "Celebrating diverse contributions", "Authentic connections with team members", "Using inclusive language and respecting identities", "Being mindful of meeting times for inclusivity", "Modeling inclusive behavior as a manager", "Holding each other accountable for DIB practices"]}, "type": "chunk"}, {"id": "2ebbbbfd-1be8-44c1-a7a7-b97e28e954e2", "properties": {"page_content": "Tips for Team Members Schedule a Coffee Chat with a Team Member Understanding GitLab is fully remote, there is an opportunity to get to know team members beyond your geographical location as well as outside of your division. This provides an opportunity to: learn more about other cultures and work divisions cultivate better global communication make progress toward building an inclusive environment Coffee chats are suggested during onboarding but you don't need to stop there. It is recommended to continue this action whenever you would like to. Please take a look at our GitLab team page and feel free to select someone for a coffee chat! Hold Each Other Accountable Hold each other accountable and speak up when someone uses non-inclusive language. We should also celebrate when we are inclusive, and strive to support each other every day. Avoid Identity Assumption Statements Identity is a personal thing. Looks or names can imply diverse attributes, but they are not perfect identity indicators. Avoid making statements about someone's perceived race, gender, age, ethnicity, or other personal characteristics. When people are typecast or feel misunderstood, they are less likely to feel that they belong. Tip to Uncover Unconscious Bias When you are dealing with a specific situation, you can mentally flip the situation around and see how that feels. For example, flip it from a woman to a man, and if it feels off then you might have a bias. By putting ourselves in someone else's shoes, we can better understand their point of view and be more supportive. More to come Consider joining a TMRG (Team Member Resource Group) Support others as an Ally Review the Diversity, Inclusion & Belonging page", "entities": ["GitLab", "TMRG", "Team Member Resource Group"], "themes": ["Team collaboration", "Remote work culture", "Inclusive environment", "Global communication", "Cultural understanding", "Accountability", "Unconscious bias", "Identity assumption", "Diversity and inclusion", "Team Member Resource Groups"]}, "type": "chunk"}, {"id": "e5fb375c-6a22-4cf5-acaa-f6dc4ef91786", "properties": {"page_content": "title: Diversity Inclusion & Belonging Communications Strategy description: PRoviding details on how the DIB Team communicates with GitLab to achieve engagement, contributions and collaborations from team members\n\nPurpose\n\nThe Diversity, Inclusion & Belonging (DIB) Team has a variety of ways that it communicates with team members from IC all the way up to e-group, this page documents the ways we do this, what information will be provided and the timing of when these communications will happen.\n\nEncouraging Participation in Diversity, Inclusion & Belonging\n\nDIB is a core value and is something that is measured by DIB Compentencies in performance reviews, mid reviews and promotions. The DIB team is providing an easily accessible way for team members to display the DIB value by providing on a monthly or quarterly basis depending on level, way you can contribute on a increasing scale of Good, Better & Best. This will enable team members to participate in a ways they are able to at any given time depending on competing priorities.\n\nDRIs & Dates & Method of Communication:\n\nE-Group Communications -\n\nDRI: <PERSON>rida <PERSON>llan\n\n<PERSON>ce: Quarterly\n\nMethod of Communication:\n\nVP+ Communications -\n\nDRI: <PERSON><PERSON>: Quarterly\n\nMethod of Communication: VP Direct Monthly Call\n\nLeadership DIB Council -\n\nDRI: <PERSON>: Monthly\n\nMethod of Communication: Leadership DIB Council Call\n\nPeople Managers Communications -\n\nDRI: <PERSON>: Monthly\n\nMethod of Communication: #People-Mrgs+ Slack Channel\n\nIndvidual Contributors Communications -\n\nDRI Marina <PERSON>rigg\n\nCadence: Monthly\n\nMethod of Communication: While you were Iterating, What’s Happening and DIB Slack Channels\n\nExample\n\nGood: Use monthly Zoom Background attached Better: Promote & Attend heritage month event on XXXX Best: Post in the #people-managers-and-above around a personal story focused on your experience with regards to XXXX\n\nWhy?\n\nWe are approaching it this way to ensure that all team members have the same opportunity to contribute to Diversity, Inclusion & Belonging regardless of their capacity to contribute. This will help provide clear and concise ways that team members can live in the DIB Value\n\nLogistics\n\nE-Group Communications - Reminder: week 8 of prior quarter Content Ready & Reviewed: 1 week prior to the start of the quarter Released:\n\nVP+ Communications - Reminder: week 8 of prior quarter Content Ready & Reviewed: 1 week prior to the start of the quarter Released: During the VP Directs Call\n\nLeadership DIB Council - Reminder: 1 week prior to LDC Call Content Ready & Reviewed: 3 days prior to the LDC Call: 1st week of the new month\n\nPeople Managers Communications - Reminder: 1 week prior to the start of the month Content Ready & Reviewed: 3 days prior to the start of the month Released: During the LDC Cal,\n\nIndvidual Contributors Communications - Reminder: 1 week prior to the start of the month Content Ready & Reviewed: 3 days prior to the start of the month Released: 1st week of the new month\n\nDIB Monthly Initiatives Call\n\nWe host the DIB Monthly Initiatives Call to provide an opportunity for Team Members to be able to ask questions around DIB Initiatives, DIB Programming and general questions regarding DIB. We also provide a deck where we highlight everything that happened in the last month and what to look forward to.\n\nProcess\n\nDRI: Marina Brownrigg, DIB Partner\n\n10 working days prior to the monthly DIB Initiatives call, create Issue, then tag all the TMRG and TMAG stakeholders/leads to add their updates in the linked deck prior to the call.\n\nDIB in Q2 - PBPs Only\n\nTo ensure strong collaboration with the People Business Partner team we provide a DIB in Q2 deck that highlights the strategic imperatives of the DIB Team and any collaborations that are required.\n\nProcess\n\nDRI - Liam McNally, Manager, DIB\n\nCadence: Quarterly\n\nRelease for Collaboration from DIB Team: 2 weeks prior to the end of the quarter\n\nDelivered: First week of the new quarter\n\nMethod of Communication: Leadership DIB Council Call DIB-PBP Slack Channel", "document_metadata": {"source": "Sample_Docs_Markdown\\dib-communications-strategy.md"}, "headlines": ["Purpose", "Encouraging Participation in Diversity, Inclusion & Belonging", "DRIs & Dates & Method of Communication:", "Why?", "Logistics"], "summary": "The Diversity, Inclusion & Belonging (DIB) Team at GitLab communicates with team members across all levels to encourage engagement and contributions. The team provides monthly or quarterly opportunities for participation categorized as Good, Better, and Best actions. Communications are managed through various channels such as E-Group meetings, VP calls, Slack channels, and the Leadership DIB Council. Each communication stream has a designated lead (DRI), cadence, and method of delivery. Additionally, the DIB Monthly Initiatives Call allows team members to ask questions and review updates. Contributions are aligned with performance reviews to emphasize DIB as a core value. Logistics include reminders, content reviews, and releases according to set timelines. A quarterly DIB in Q2 deck fosters collaboration with the People Business Partner team.", "summary_embedding": [0.06797047, -0.10494094, 0.0661748, -0.02695069, -0.01839659, -0.05341078, -0.05150342, -0.07521612, 0.09244797, 0.00086449, -0.01239284, 0.0884744, 0.05173917, -0.06718224, 0.11487748, -0.04356226, -0.11687128, 0.10987534, 0.02986319, -0.01881901, -0.01881165, -0.09714057, -0.086279, 0.05854125, -0.04204642, -0.00596663, 0.05354352, -0.03631346, 0.06136701, -0.01596035, 0.18225166, -0.00650898, -0.04319217, 0.07615561, 0.00086151, 0.04793351, -0.00685097, 0.02983681, 0.02757825, -0.01928353, -0.00226102, 0.04081318, -2.623e-05, 0.01121096, 0.12177319, -0.03874245, 0.01044093, 0.0217896, 0.04226325, -0.06400283, 0.0648365, 0.10357831, 0.1224914, 0.03759911, -0.00750513, 0.09717445, -0.01793611, -0.04456034, 0.01551299, -0.02464442, -0.03008028, 0.02887596, -0.02253843, -0.0751181, 0.0190236, 0.09716109, 0.02256818, 0.00083365, -0.03562318, -0.03957961, 0.01529032, -0.01685795, -0.0618406, 0.09532934, -0.01010102, 0.02313687, 0.01153435, -0.04183704, -0.06328459, 0.01042864, -0.01420314, 0.01710758, 0.05157959, -0.03054712, -0.02412091, -0.0367383, -0.08099686, -0.02536646, -0.04264597, 0.0536513, 0.07172845, -0.03775406, -0.04991081, 0.0165255, -0.02861825, 0.04367317, 0.0510864, -0.04279723, 0.05445557, -0.04900637, 0.05736328, 0.00751207, -0.04955673, -0.04292507, 0.03343685, 0.00091924, 0.11619797, -0.02333093, 0.03914857, -0.06929708, -0.00994369, 0.09183798, -0.01243457, -0.00037832, 0.02260895, -0.04257131, -0.00599033, -0.00529143, -0.01736903, -0.07146693, 0.03978844, 0.03838715, -0.07079681, 0.0276256, 0.01723456, -0.03375256, -0.02652127, 0.07132462, -0.00815255, -0.03231628, -0.01750853, 0.00950649, 0.05289992, -0.06172474, 0.00648462, 0.03656204, 0.00370737, -0.03167792, -0.02754333, 0.00921809, -0.03927781, -0.02743698, -0.0409121, 0.0555924, -0.04831146, 0.00519925, 0.00117155, -0.00328329, -0.03678913, 0.05475257, 0.00303457, 0.04183516, -0.03648388, 0.0086166, -0.03457528, -0.02021608, 0.00648575, 0.01179334, 0.05348514, 0.01439032, 0.0414495, -0.00888659, -0.00079278, -0.03307041, 0.03657059, -0.02901108, -0.0191384, 0.05281112, 0.03245989, -0.02518841, 0.00623746, 0.06747439, -0.0045561, 0.01975, 0.01193894, -0.05634015, -0.03812534, 0.0259174, -0.00705791, 0.04659505, -0.0263804, -0.01674408, -0.02209092, 0.0031504, -0.01644733, 0.04587514, 0.01357566, 0.01343168, -0.03136437, 0.03433432, -0.04648408, 0.0177926, 0.01611912, -0.00012009, -0.07024737, 0.0254472, -0.0275391, -0.01214561, 0.00234869, 0.01019683, 0.05518457, 0.01833373, 0.02282676, 0.00427424, 0.05451316, 0.02771711, 0.00225667, 0.00982007, -0.00011064, -0.00230681, -0.02490954, 0.05146635, 0.0215802, 0.01425776, -0.00822487, 0.04728884, -0.00509463, 0.03087214, 0.02722153, -0.05086059, -0.05952387, -0.00451331, 0.03682158, 0.02937951, 0.00468348, -0.05261147, -0.05502069, 0.03991175, 0.04256246, 0.01772278, -0.05191611, 0.00084083, 0.04549858, -0.01487728, 0.01577652, -0.07428945, 0.04697963, -0.01332133, 0.03908888, 0.00887854, 0.03175397, -0.06912032, 0.01457562, -0.03012278, 0.00935123, 0.0190931, 0.00422931, 0.05724607, -0.07878777, 0.00046603, -0.07132662, -0.04500351, -0.01072764, -0.03927335, -0.01730555, 0.01433435, 0.02704718, -0.04836455, -0.03682577, 0.03908482, -0.0040825, -0.01056792, 0.00360033, 0.01199252, 0.06043823, 0.04831796, -0.00983323, -0.00595163, -0.03063745, 0.00135367, -0.01319509, -0.01022795, -0.0337293, -0.01486821, -0.01542958, -0.00268514, -0.00037086, -0.02512957, 0.02441801, -0.0143891, -0.06926554, 0.007266, -0.01019272, 0.01217375, -0.03501882, 0.00373729, 0.06007472, -0.025549, -0.03598493, 0.00071858, 0.02916368, -0.00076566, -0.01607127, -0.00324453, 0.0256378, 0.00046651, 0.00437171, 0.00107635, 0.01474368, 0.00682049, -0.03745082, 0.01906063, 0.02189065, -0.03131185, 0.02135416, -0.03796246, 0.00620112, -0.05904301, -0.02437649, -0.00360658, 0.02825775, 0.04231437, -0.0238446, -0.0122563, 0.01520119, -0.0050962, 0.00585598, -0.0147209, -0.00681275, 0.00309833, 0.00051825, 0.01675412, 0.00912138, 0.00945717, 0.01763804, 0.00274531, -0.02092694, -0.05654359, -0.03067186, 0.01982669, -0.01537638, 0.02967895, 0.00499642, 0.01220005, -0.00221522, -0.06461487, -0.03090725, -0.00587457, 0.00733964, 0.03804503, -0.01864518, 0.00775774, 0.02015228, 0.0126663, -0.00018213, -0.01159438, 0.00329293, -0.0199749, 0.01841783, -0.00167092, -0.0471251, -0.02337885, -0.03706693, -0.01707729, 0.03466124, 0.04921003, -0.04767872, 0.01337927, -0.0193029, 0.01287661, 0.04132108, -0.01323893, -0.01504276, 0.04351759, -0.02725644, -0.02647338, 0.02974348, 0.02767588, -0.01849944, 0.0389166, 0.03996786, 0.0019551, -0.01340523, 0.04228092, -0.00035934, 0.00288784, 0.03150836, 0.00280081, -0.00869851, -0.00060781, -0.00756206, 0.00781306, 0.01509058, 0.00086326, 0.03153444, 0.00202921, 0.04060397, -0.02377614, -0.00549742, -0.00354648, 0.05485643, 0.02984171, -0.03072767, 0.02920229, -0.04025595, -0.03949922, -0.04291892, -0.02307088, 0.00726105, 0.05268086, 0.03419212, 0.0519347, 0.02935657, -0.06645996, 0.02929985, 0.00801553, -0.00146317, 0.01356423, -0.0722601, 0.00056353, 0.01523624, 0.01585143, 0.00333707, 0.03969943, -0.02438067, 0.03814715, 0.02234597, 0.02927602, 0.00411888, -0.00694055, 0.00101571, -0.02582837, 0.0210092, 0.00174996, 0.01354366, -0.00582033, -0.01412731, -0.03252246, 0.06384261, -0.02583926, 0.03993027, 0.02065356, 0.01287649, -0.0429963, -0.02556775, -0.00208388, 0.05144611, 0.02573985, 0.02919043, -0.02816162, 0.06097981, 0.04282391, -9.66e-06, -0.00989145, -0.03423126, 0.01731553, -0.01720479, -0.02159684, 0.00979459, -0.02118927, 0.00673963, 0.04780615, 0.04967996, 0.02969193, 0.03041105, 0.01565476, -0.00525085, 0.0065933, -0.01225574, 0.03596913, 0.00836435, -0.0443676, 0.05040896, -0.01491778, 0.01884162, 0.03545071, -0.00147429, 2.94e-05, -0.00587471, 0.04192535, 0.03403611, -0.01504006, -0.02245202, 0.00882016, -0.03270745, 0.03814986, -0.02202263, 0.05044487, -0.01284849, -0.01274962, 0.0245967, 0.00567564, -0.02892656, -0.00474728, -0.02152905, -0.06187028, -0.00942126, 0.02963423, -0.0385587, -0.02700806, -0.00261389, -0.01109563, -0.00889598, 0.04352546, -0.06919312, 0.03937107, -0.02126465, -0.00814207, 0.01665228, -0.01453334, -0.01242195, 0.02315259, -0.03805708, -0.02127083, -0.02843748, -0.05061163, 0.00085463, 0.01162923, -0.00639403, 0.06939594, -0.04579307, -0.04126225, -0.00163027, -0.06036352, 0.031095, 0.00932936, 0.02032882, 0.00586239, -0.01084191, -0.01053642, -0.02808494, 0.00107519, -0.00600803, 0.03770085, -0.00290822, -0.01172632, 0.03835711, -0.00914398, -0.02070301, -0.01554982, -0.02057716, 0.04065158, 0.01104983, 0.01475065, -0.03128187, 0.00983318, 0.00367789, -0.03379828, -0.01936591, 0.0256963, -0.00569548, -0.03646895, -0.02543365, -0.0109904, 0.00098288, 0.02542776, -0.01143914, -0.04314453, 0.07094926, 0.04123081, -0.03217999, 0.03642514, 0.00906871, 0.014125, 0.00688079, -0.00031372, -0.02135473, -0.02301645, -0.02419047, 3.223e-05, 0.01593025, 0.01094683, 0.0127356, -0.05367605, 0.01807793, -0.01206974, 0.00591765, 0.02840594, -0.06254265, -0.02029849, 0.01863712, 0.01428429, 0.01371514, -0.00645965, 0.0010173, -0.00343174, 0.00203125, 0.01370478, 0.01009799, -0.03357826, -0.00299221, -0.01725226, 0.00934974, -0.0003769, -0.02865886, -0.03845023, -0.02308129, -0.01579187, -0.01671533, -0.03753078, 0.00239554, 0.03198307, -0.0345738, 0.05480913, -0.01856723, 0.0228895, -0.03471841, 0.00290454, -0.0478507, 0.00671883, 0.00166247, -0.00597649, -0.0105667, 0.01009831, -0.00206969, 0.02764739, -0.0008133, -0.0255858, -0.01747408, -0.01873424, 0.01495721, 1.78e-05, 0.00837797, 0.01676433, 0.05107704, -0.0109096, 0.01352238, -0.01946045, 0.00190801, -0.00621456, -0.00666079, -0.03253872, 0.02291863, -0.01046167, -0.02399763, -0.03285688, -0.00499817, 0.03602733, -0.05144479, 0.02341805, 0.01030608, 0.01322296, -0.02317725, -0.02914202, 0.04124955, -0.03272471, 0.03783236, -0.01830423, -0.01510335, -0.03442887, -0.00129075, -0.03031958, 0.00767767, -0.02108836, -0.00770007, -0.02682243, 0.0288019, -0.00182429, 0.01929135, -0.02285151, -0.0135852, 0.00739786, 0.0064266, 0.02915701, -0.00388001, 0.02524162, -0.00705377, -0.01369295, 0.01108351, -0.01206077, 0.00970223, -0.03828929, 0.01832832, -0.00474396, -0.01008921, -0.02890876, -0.01327186, 6.605e-05, -0.03119288, 0.0182344, -0.01172026, -0.04716729, 0.01109472, 0.01027848, 0.01015031, -0.04626976, -0.00692487, 0.00097044, -0.00281271, 0.00437738, -0.00499602, -0.04222728, 0.02382634, 0.04525067, -0.00973998, -0.03872738, -0.00472798, -0.00971073, 0.02397499, 0.02139178, 0.03626806, -0.00416365, 0.02730048, -0.01319034, 0.03280174, -0.01108217, -0.00753316, 0.01209542, -0.00855089, 0.00071933, -0.01388934, 0.03098911, -0.00791573, 0.02806021, -0.00841825, 0.03623374, 0.00797011, -0.01479262, -0.00474579, 0.03845485, -0.02799071, 0.02176743, 0.02151952, -0.03102889, -0.00173726, 0.00332908, -0.02443824, -0.00621481, 0.0017817, -0.03668433, 0.01299878, -0.03427711, 0.04705196, 0.01146771, -0.0592289, 0.00935148, 0.02478137, -0.00898628, -0.0151971, -0.00021704, -0.00979509, -0.0496454, -0.01259568, 0.04685331, 0.00823054, -0.04657338, 0.03519598, -0.00014168, -0.01741064, -0.00462518, -0.03843319, 0.01579607, -0.01010991, 0.05267106, 0.02175674, 0.002286, -0.01434663, -0.02037688, 0.05427026, 0.0038558, -0.00074013, -0.03020681, 0.01268713, -0.01059059, 0.00157148, -0.00946558, 0.02681494, 0.01152969, 0.00294813, -0.01297393, -0.01756821, -0.00836012, -0.00324383, -0.02856639, -0.01916351, -0.01662203, -0.00716523, -0.02505306, -0.0346529, 0.01536275, 0.00364985, -0.01519363, -0.00700218, -0.00726711, 0.02752309, 0.02434315, -0.01560437, 0.01051072, -0.02751957, 0.02084374, -0.01711968, 0.02341251, -0.00527599, 0.02050861, 0.04382763, -0.03147667, -0.00592846, -0.01091633, -0.00287138, -0.02663323, -0.0175451, -0.04048759, 0.02059835, 0.03528365, -0.02332066, 0.02162476, -0.01681226, 0.00072439, 0.00157075, 0.03892632, -0.00855442, -0.01011202, -0.00812779, 0.00457406, 0.0331736, 0.02723145, 0.0160742, 0.00365434, -0.02762209, -0.02528835, -0.00081766, -0.01040086, -0.00344983, -0.0337716, -0.00696498, 0.01080109, -0.00569661, -0.00593225, 0.00937229, 0.02947891, 0.01794221, 0.02129364, 0.01866928, 0.00306905, -0.01708655, 0.0401002, -0.00250787, -0.0124685, 0.01819113, 0.0099715, 0.03173532, 0.00458344, 0.02244153, 0.03728429, 0.00265345, -0.03096476, 0.03762584, 0.01910332, 0.01316527, -0.00085426, 0.02035143, 0.01248806, -0.00840524, 0.00829611, -0.0154144, -0.03690045, -0.02118967, 0.05676707, -0.026688, -0.01916827, -0.00037236, 0.02027692, 0.0020688, 0.00617309, -0.01175926, -0.01310492, -0.05662923, -0.0246545, 0.02308924, -0.00616676, -0.03606028, 0.0030843, -0.04643032, -0.00037308, 0.00226731, 0.02723267, -0.00641249, 0.00548624, -0.02759775, -0.00998728, -0.00367152, -0.00047923, 0.02300159, -0.01274798, -0.01503071, -0.00906648, -0.01050118, -0.02434125, -0.02754172, -0.01876899, -0.00176442, 0.00635554, -0.01325497, -0.01136044, -0.0194735, -0.00130377, 0.03180855, -0.02332821, 0.04146826, 0.01748245, 0.00755566, -0.00220623, 0.01338208, -0.02358043, 0.00028762, 0.00580574, 0.00964128, 0.02787754, 0.00765473, -0.00551402, -0.00116911, 0.01635243, -0.01073324, -0.00888704, -0.05770982, -0.0107391, 0.01480895, -0.01287359, -0.01888748, -0.0244765, 0.03899106, -0.00467502, 0.00504998, 0.01240796, 0.0014021, -0.01064106, 0.0604698, 0.0151321, 0.02324766, -0.00152393, -0.01043158, 0.00964391, -0.00224549, 0.00867666, -0.00779988, 0.00706047, -0.00246537, 0.00149617, -0.00169436, -0.00208922, -0.02157113, 0.02289606, -0.0020164, -0.01007822, 0.04658185, 0.00074984, -0.0018874, -0.01337903, -0.01713636, -0.00507113, -0.04635731, 0.01029883, -0.01635501, -8.768e-05, -0.01517291, -0.00338111, -0.00624098, -0.00685062, -0.00368883, -0.01494935, -0.03598458, 0.04945093, -0.01236574, -0.01957308, 0.00867462, 0.0028053, -0.04947693, 0.00278071, 0.00385718, 0.02937069, -0.02399046, -0.02711007, 0.02165371, 0.02529706, 0.05057017, 0.01627723, -0.03798836, 0.01632139, -0.03210348, 0.02450759, -0.02394188, -0.00331002, 0.01072623, 0.01239341, 0.0070217, 0.04827769, -0.0177302, -0.00236635, -0.00032409, -0.00701793, 0.01934899, -0.00262227, 0.02572818, 0.00133373, 0.02865548, 0.00313827, -0.00708027, -0.01912789, -0.02590391, 0.00969141, 0.028759, 0.02818935, -0.06175613, -0.0139698, 0.02163432, 0.01947884, -0.00361984, 0.00407343, -0.02237936, -0.0036008, 0.01124219, -0.02705728, -0.00779921, 0.00753499, -0.03587866, 0.00765634, 0.0095334, -0.01024347, -0.00921534, 0.01565028, -0.04650006, 0.04065736, 0.01251307, -0.01164644, 0.01402762, 0.01231175, 0.01563683, -0.01346989, -0.00231661, -0.03404611, -0.03808736, 0.00538173, 0.00623989, 0.00112736]}, "type": "document"}, {"id": "58094ce6-70c1-425e-bd31-a3924ba65aa1", "properties": {"page_content": "title: \"Roundtables\" description: On this page you will be provided an overview of our Diversity, Inclusion and Belonging Roundtables. A DIB roundtable is a great way to build deeper connections with team members and develop safe spaces to discuss DIB related issues. The DIB roundtable will ask team members to share stories and anecdotes as well as challenge team members to think about how they personally and collectively can positively impact DIB. We have two avenues for DIB Roundtables: DIB Programmed Roundtables - The DIB Team creates a programmed roundtable(s) on a quarterly basis to discuss a pre-defined topic. Self-Organized - A TMRG, Team Members, Managers can organized a roundtable to discuss DIB related issues. Starting a roundtable Diversity, Inclusion & Belonging Team Programmed The DIB Team will organize DIB Roundtables on a quarterly basis with a maximum of two roundtables in a quarter. The DIB Team will program the quarterly roundtable on the team calendar when the decision has been made on the topics for the quarter The DIB team will create an agenda with a series of questions for the quarterly roundtable The roundtable will last 50mins and be split as followed: 10mins: DIB Facilitator will set the topic and provide some context 30mins: The DIB Facilitator will divide the group into small groups of 5/6 team members to discuss the questions in the agenda 10mins: Debrief The DIB Team will ask for a volunteer or are elect group facilitator for each group, your role is to: Verbalize the questions Ensure that everyone, including yourself gets an opportunity to contribute Share your screen where necessary i.e. slides or tasks Programmed Roundtables: Date & Times Subject DIB Facilator TMRG (if applicatble) TBC Allyship Liam McNally N/A TBC Bias Liam McNally N/A TBC Microaggressions TBC NA Self-organized First, identify a group of team members who would like to participate. This can be done via slack, with your direct team or other avenues. Open an issue using this template and invite the roundtable members Add DIB Roundtable Label Tag @gitlab-com/people-group/dib-********************and-belonging for visibility Assign or elect someone who will help facilitate the conversation If you volunteer or are elected to be the facilitator you role is to: Verbalize the questions Ensure that everyone, including yourself gets an opportunity to contribute Share your screen where necessary i.e. privilege for sale task Manager Organized A manager can request the DIB Team organize a DIB Roundtable for their team. They will help facilitate the roundtable and will organize other activities specific to the needs of the team. If you are a manager, request a DIB roundtable by using this issue template. A DIB Team Member will set up a time to discuss with the manager the function of the DIB Roundtable and suggest activities and exercises to achieve the aim of the Roundtable. The Roundtable Set the ground rules This is a closed session and a safe space. However if something arises as part of the conversation that needs investigation, the roundtable facilitator will reach out to their PBP to discuss. Assume positive intent, share and express with a higher intent Learning happens through experience; so if someone makes a mistake, give feedback kindly Please avoid multitasking unless there is a true emergency; please give your full attention to the session. Start with Privilege for Sale or Privilege Backpack Privilege for Sale is a great interactive activity to get you thinking about Diversity, Inclusion and Belonging. Click the link for the full activity. The Goals and learning objectives are as follows: To acknowledge and investigate privilege. To provide an opportunity for team members to connect and reflect on the experience of having (or not having) privilege. To discuss the variety of privileges that underrepresented groups have limited access to. Not just legal privileges but social, financial, etc. To discuss how no one privilege is more important than another, that for someone any privilege may feel essential. Team members will be able to identify privileges that they take for granted in their everyday life. Team members will discuss what types of privileges (social, financial, legal, etc.) are important to them and why that may differ from others in their group. Team members will be able to investigate and discuss what groups may have limited access to what privileges and effect that lack of access may have on an individual. In the activity you have a list of privileges, you do not have any of these privileges, for the purpose of this exercise. Roundtable Questions What are some of your most important values? What resonates with you? Share a time when you felt like you were left out or didn’t belong? What are some of the common threads, behaviors that made you feel like this? Share a time where you felt empowered or belonged? What were the actions/behaviors that led to you feel empowered or that you belonged? What does Diversity, Inclusions and Belonging mean to you? Why is it important? What is one thing as a group can we do to make a positive impact on Diversity, Inclusions and Belonging? Once you have fully completed the roundtable, create a handbook entry on the DIB Roundtable Sharing Page. You can share as much or as little as your would like too about the experience and learnings of the roundtable.", "themes": ["Diversity", "Inclusion", "Belonging", "Roundtables", "Team Engagement", "Safe Spaces", "Facilitation", "Privilege", "Interactive Activities", "Learning Objectives"], "entities": ["DIB", "TMRG", "<PERSON>", "PBP", "Privilege for Sale", "Privilege Backpack", "Gitlab"]}, "type": "chunk"}, {"id": "6593e690-298f-4d7b-a8d9-94a09bff2ca4", "properties": {"page_content": "What next? The Diversity, Inclusions and Belonging Team is working on Roundtables part two, where we will go beyond creating safe spaces to discuss DIB related issues, to developing deeper trust, empathy and vulnerability.", "themes": ["Diversity", "Inclusion", "Belonging", "Roundtables", "Safe spaces", "Trust", "Empathy", "Vulnerability", "Discussion", "Development"], "entities": ["Diversity", "Inclusions and Belonging Team", "Roundtables"]}, "type": "chunk"}, {"id": "725a4936-bc3a-47cf-a189-ea2d6d91b3c6", "properties": {"page_content": "title: \"Sales Sponsorship Pilot Program\" description: \"A page for the Sales Sponsorship Pilot Program\" In 2023 we conducted a pilot program to address an imbalance of Black Team Members at job grade 9+ within Sales and lack of representation in sales leadership positions. The pilot program was designed to create pathways for Black Team Members to progress effectively at GitLab. We have since extended this program to promote pathways for all Sales team members to progress at GitLab. Mentorship vs Sponsorship Harvard Business Review defines the difference between sponsors versus mentors: “While a mentor is someone who has knowledge and will share it with you, a sponsor is a person who has power and will use it for you.” Mentoring is defined in our Mentoring at GitLab handbook page as: Mentor relationships are an opportunity for individuals to learn from someone's personal experience, background, and perspective. These relationships build trust on a team, provide safe space to make mistakes, and encourage both personal and professional development. Mentorship is an opportunity for both the mentor and mentee to develop their leadership and communication skills. Mentorship should be led by the mentee, similar to how 1:1's at GitLab are driven by direct reports. Sponsorship is defined using influence and/or power to support the growth and development of a team member. A sponsor is typically a senior leader with significant influence and responsibilities. A sponsor will then use this influence to but not limited to; advocating for career advancement opportunities, provide opportunities to upskills via active projects and provide visibility to other senior leaders and the business of the sponsees potential. Role of a Sponsor at GitLab A Sponsor is someone who has power and influence at GitLab and will use that power to advocate, elevate and impact a team members opportunities and career progression at GitLab. A sponsor at GitLab is: A Senior Leader at a minimum job grade 10+ and is not the sponsees direct manager. The senior leader should be a People Manager or Manager of Managers Must have been at GitLab for 6+ months Be able to commit to a 9 month sponsorship program which will include Sponsor Training Kick-off calls Bi-weekly 1-1 with Sponsee Be able to fully commit and be held accountable for their role within the Sponsorship Program As a Sponsor you should be: Be open and honest Listen and Learn Lean into discomfort in areas not familiar to you (e.g. cultural differences) Take risks with the relationship Advocate for your sponsee with intent Give feedback as appropriate Be intentional in building the relationship A sponsor will: Use their influence and power to advocate for the sponsee potential for career advancement Use their influence to increase the visibility of the sponsee amongst other Senior Leaders at GitLab Use their influence to increase the visibility of the sponsee at GitLab Provide opportunities and space for risk-taking and growth Provide feedback on growth and skill development The sponsorship relationship may not provide all the above but the sponsor should be willing and able to provide some of the opportunities above to the sponsor.", "themes": ["Sales Sponsorship Pilot Program", "Mentorship vs Sponsorship", "Career Advancement", "Leadership Development", "Diversity and Inclusion", "Professional Growth", "Influence and Power", "Senior Leadership", "Accountability", "Feedback and Skill Development"], "entities": ["Sales Sponsorship Pilot Program", "GitLab", "Harvard Business Review", "Black Team Members", "Mentoring at GitLab", "Senior Leader", "Sponsorship Program", "sponsee", "1:1's", "People Manager"]}, "type": "chunk"}, {"id": "bb993c58-9e3a-4a41-baef-ead3b7d7636f", "properties": {"page_content": "Sponsorship Program As a Sponsor you should be: Be open and honest Listen and Learn Lean into discomfort in areas not familiar to you (e.g. cultural differences) Take risks with the relationship Advocate for your sponsee with intent Give feedback as appropriate Be intentional in building the relationship A sponsor will: Use their influence and power to advocate for the sponsee potential for career advancement Use their influence to increase the visibility of the sponsee amongst other Senior Leaders at GitLab Use their influence to increase the visibility of the sponsee at GitLab Provide opportunities and space for risk-taking and growth Provide feedback on growth and skill development The sponsorship relationship may not provide all the above but the sponsor should be willing and able to provide some of the opportunities above to the sponsor. Role of a Sponsee A Sponsee at GitLab will be (Pilot Program Only): Be performing in your role in accordance with your managers expectations, which take into account performance exercises such as 9 box and not on a Performance Improvement Plan Be a part of the underrepresented group the sponsorship program is targeted at. Be able to commit to 9 months within the program Willing to commit to the overall expectations of the program as outlined throughout the handbook page As a Sponsee you should be: Be able to lead the relationship with your sponsor Be able Take risks with the relationship—trust is a vital part of the relationship Be able to do the work required based on feedback received and requirements for success Be able to request and give feedback as appropriate Be able to take personal responsibility for your career and be empowered to ask for what you need A Sponsee will: Maintain or exceed their performance record Maintain trust & confidentiality in the relationship Assist the sponsor in insights to the business and challenging perceived norms Be an ambassador for our CREDIT Values Have aspirational goals for career development; within leadership or as a senior IC Have a growth mindset and will be able to continually learn Have the ability to take on special projects which will impact their growth Makes themselves available for opportunities and stretch assignments Consistently performing at or above the performance bar Role of Sponsees Manager The manager will support the team member in ensuring that have a Individual Growth Plan (IGP) As well reassess the IGP at regular intervals during the sponsorship program The manager will regularly check in on the progress of the Sponsorship Program with their report The manager will regularly report on the success of the program to the leadership advocates of the program Participate in a manager of sponsees session Sponsorship Program The program will last 6 months and the Sponsor & Sponsee will be the same person throughout the program. The program will consist of: An Individual Growth Plan is created in conjunction with your direct manager. The sponsor will add further suggestions throughout the program 1-1 conversation in a cadence agreed upon by the participants. We suggest a minimum of bi-weekly. Special projects & Sponsorship activities may mean cadence changes Quarterly All-Hands This will reinforce the ideals of the sponsorship program and ensure that the relationship is progressing through the phases. Individual Quarterly Feedback with DIB Team End of Program session with Sponsor Complete Next Step Documentation with Sponsor & Direct Manager to ensure progression continues Sponsor Sponsee Matching: A sponsor should be at least two job grades higher than the sponsee A sponsor and sponsee should be in a timezone that is practical to have 1-1s. Special projects etc can follow asynchronous principles If possible Sponsor should align with the career development plan of the sponsee", "themes": ["Sponsorship Program", "Mentorship", "Career Development", "Advocacy", "<PERSON><PERSON><PERSON>", "Trust", "Cultural Differences", "Risk-Taking", "Growth Mindset", "Individual Growth Plan"], "entities": ["GitLab", "Sponsorship Program", "Senior Leaders", "CREDIT Values", "Individual Growth Plan", "IGP", "DIB Team"]}, "type": "chunk"}, {"id": "0690fc16-c737-4967-95f1-7378c3aaea38", "properties": {"page_content": "What does a successful sponsorship look like? Build Take the time to build a solid relationship with each other. This will be particularly important if you have no previous direct working relationship and can often take some time to cultivate. It is very important to build the relationship first before moving into authentic sponsorship. Suggested Actions: Commit to regular 1-1s Understand the sponsees career development plan Set goals and expectations early Develop You have taken the time to build a relationship with each other, the next step is to develop that relationship by becoming action & capability focussed. In this step the sponsor will help guide the sponsee on areas of improvement in skills & capabilities. The sponsee is responsible for acting on feedback and being intentional about displaying these improvements to the sponsor. Suggested Actions: Find and seek opportunities for the sponsor to observe the sponsees improvement areas Sponsee invites Sponsor to a team meeting where they are presenting Sponsor invites sponsee to a working group Commit This is where both parties agree to move forward with the next step, which is sponsorship and advocating for the sponsee. This can take many forms, such as; a formal discussion, the sponsor outlining actions to the sponsee or at the sponsees request. This is an opportunity to provide feedback, any uncertainties and to reestablish career development goals. Suggested Actions: Participate in feedback session with sponsee Advocate Now that a commitment has been made and the sponsor is satisfied that the sponsee is ready for the next step. The sponsor actively and intentionally advocates for sponsees continued career development and advancement at GitLab. Goals & Benefits of Sponsorship Program Goals: The goal of this program is to provide team members from underrepresented groups opportunities to have more visibility at GitLab. We are starting with the Black team member population in sales as they are very underrepresented in all areas of leadership. The goal is that programs like this will help redress the imbalance and ensure we are moving towards a more diverse, equitable and inclusive workplace. Benefits: Sponsor: Exposure to a diverse set of team members at GitLab, increasing their ability to lead diverse teams Exposure to new ideas and be challenged on the status quo Intentional talent management & succession planning Better understanding of the challenges of team members from underrepresented groups Sponsee: Increased exposure to GitLab and visibility to senior leaders at GitLab Access to feedback from a senior leader Ability to develop skills and capabilities Increased control over your career development plan GitLab: A more diverse team at leadership team Increased retention of team members Attraction of underrepresented groups to GitLab Team members more motivated to maintain performance levels Measurables Start, Mid and End of the program - Satisfaction score from both Sponsor and Sponsee Career Advancement Rate within twelve months of the program Pilot Project Plan Phase 1: Develop training materials for Sponsors & Sponsees => 0% Develop program materials => 0% Sponsorship 1-1 guide => 0% Next Steps Doc - for End of Program => 0% Identify first cohort of sponsees amongst Team Members in Sales => 0% What criteria they have for a sponsor => 0% Identify first cohort of sponsors with the Sales Org => 0% What expertise they are willing to provide and opportunities they can provide => 0% Matchmaking of Sponsors & Sponsee => 0% Complete => => 0% Phase 2: Conduct Kick-Off call to present the program => 0% Conduct Sponsorship Training => 0% Release Sponsorship Matches => 0% Conduct Pre-Start AMA => 0% Phase 3: Begin the Sponsorship Program => 0% Quarterly check in call - Group => 0% Quarterly feedback individual check in => 0% Next Steps Doc completion => 0% End Program => 0% Phase 4: Retrospective on Program Success/Improvements => 0% Final feedback from Participants => 0% Provide iteration recommendations => 0%", "themes": ["Sponsorship", "Relationship building", "Career development", "Mentorship", "Advocacy", "Diversity and inclusion", "<PERSON><PERSON><PERSON>", "Skill development", "Leadership", "Program evaluation"], "entities": ["GitLab", "Black", "sales", "leadership", "Sponsor", "Sponsee"]}, "type": "chunk"}], "relationships": [{"id": "da321735-702f-4f1a-9477-da46226fa002", "type": "child", "source": "2bf75794-91b1-478a-bd94-495c589f3f35", "target": "e489e791-3f7d-4034-9680-9e6cc40813c3", "bidirectional": false, "properties": {}}, {"id": "4f0bce36-5dad-4c3e-ad21-fb9e374d050b", "type": "child", "source": "2bf75794-91b1-478a-bd94-495c589f3f35", "target": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "bidirectional": false, "properties": {}}, {"id": "71493bf8-5da9-465e-99f8-b7bd6f15dc09", "type": "child", "source": "2bf75794-91b1-478a-bd94-495c589f3f35", "target": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "bidirectional": false, "properties": {}}, {"id": "128320d1-af6b-44a8-a422-2c77d93e2fe7", "type": "child", "source": "2bf75794-91b1-478a-bd94-495c589f3f35", "target": "********-fd84-453a-a35e-08efcfc73dd9", "bidirectional": false, "properties": {}}, {"id": "cbaf5099-8fad-4c48-a150-4f99e4be650b", "type": "next", "source": "e489e791-3f7d-4034-9680-9e6cc40813c3", "target": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "bidirectional": false, "properties": {}}, {"id": "57cbe744-901a-490b-9e47-95a2b3839720", "type": "next", "source": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "target": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "bidirectional": false, "properties": {}}, {"id": "48db86a1-ed08-4497-b669-2b3cde4ca319", "type": "next", "source": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "target": "********-fd84-453a-a35e-08efcfc73dd9", "bidirectional": false, "properties": {}}, {"id": "a3fe7871-b1f9-4f95-80c6-83dfc4f73e3b", "type": "child", "source": "fc144afa-006a-45bb-aa05-ff5c7d035dc6", "target": "f06c35f4-a311-4720-bb05-71268d20ef2c", "bidirectional": false, "properties": {}}, {"id": "8fb923c2-4e34-4bde-af28-5ee91d5b3864", "type": "child", "source": "fc144afa-006a-45bb-aa05-ff5c7d035dc6", "target": "ae64034c-c2a1-4491-9785-79aacd8aa7f1", "bidirectional": false, "properties": {}}, {"id": "c54c857e-afe4-47ef-9b82-c7a211f5cbad", "type": "child", "source": "fc144afa-006a-45bb-aa05-ff5c7d035dc6", "target": "2ebbbbfd-1be8-44c1-a7a7-b97e28e954e2", "bidirectional": false, "properties": {}}, {"id": "15b39f46-6fb2-49fe-b36f-b500455f459f", "type": "next", "source": "f06c35f4-a311-4720-bb05-71268d20ef2c", "target": "ae64034c-c2a1-4491-9785-79aacd8aa7f1", "bidirectional": false, "properties": {}}, {"id": "a34d5aa9-7446-4c6b-8843-e2e7dd6bfbe3", "type": "next", "source": "ae64034c-c2a1-4491-9785-79aacd8aa7f1", "target": "2ebbbbfd-1be8-44c1-a7a7-b97e28e954e2", "bidirectional": false, "properties": {}}, {"id": "adae9277-89b5-45b7-afa5-ab5dce6d2ec1", "type": "child", "source": "75069c2c-5b1e-4844-b8eb-0d44238cfdd3", "target": "58094ce6-70c1-425e-bd31-a3924ba65aa1", "bidirectional": false, "properties": {}}, {"id": "516230fa-3ea0-4cdf-ad84-f21287b0ee3b", "type": "child", "source": "75069c2c-5b1e-4844-b8eb-0d44238cfdd3", "target": "6593e690-298f-4d7b-a8d9-94a09bff2ca4", "bidirectional": false, "properties": {}}, {"id": "3432469a-c968-4949-b114-8d510687e7ef", "type": "next", "source": "58094ce6-70c1-425e-bd31-a3924ba65aa1", "target": "6593e690-298f-4d7b-a8d9-94a09bff2ca4", "bidirectional": false, "properties": {}}, {"id": "52dfff4b-9857-45e4-9a75-bc34a772f285", "type": "child", "source": "f440cb9f-781e-43da-943a-5d3b53a720be", "target": "725a4936-bc3a-47cf-a189-ea2d6d91b3c6", "bidirectional": false, "properties": {}}, {"id": "7514b7ff-b88d-449e-94ef-8b2db4cdc532", "type": "child", "source": "f440cb9f-781e-43da-943a-5d3b53a720be", "target": "bb993c58-9e3a-4a41-baef-ead3b7d7636f", "bidirectional": false, "properties": {}}, {"id": "a72fa762-13b1-48fa-ada2-deb582e0853c", "type": "child", "source": "f440cb9f-781e-43da-943a-5d3b53a720be", "target": "0690fc16-c737-4967-95f1-7378c3aaea38", "bidirectional": false, "properties": {}}, {"id": "ec01d07d-02a3-4159-bec8-0fc5a30f4210", "type": "next", "source": "725a4936-bc3a-47cf-a189-ea2d6d91b3c6", "target": "bb993c58-9e3a-4a41-baef-ead3b7d7636f", "bidirectional": false, "properties": {}}, {"id": "13df50c3-d759-4bdf-a2e2-ed4918c6e957", "type": "next", "source": "bb993c58-9e3a-4a41-baef-ead3b7d7636f", "target": "0690fc16-c737-4967-95f1-7378c3aaea38", "bidirectional": false, "properties": {}}, {"id": "29128196-bd57-4325-af23-fad143b6d135", "type": "cosine_similarity", "source": "e5fb375c-6a22-4cf5-acaa-f6dc4ef91786", "target": "e5fb375c-6a22-4cf5-acaa-f6dc4ef91786", "bidirectional": true, "properties": {"summary_similarity": 1.0}}, {"id": "6e6f1306-06fc-43d0-a031-ee7ab6bd6ec7", "type": "entities_overlap", "source": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "target": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "bidirectional": false, "properties": {"entities_overlap_score": 0.07777777777777778, "overlapped_items": [["<PERSON><PERSON>", "<PERSON><PERSON>"], ["Tesla", "Tesla"], ["SpaceX", "SpaceX"], ["Europe", "Europe"], ["Asia", "Asia"], ["Berlin", "Berlin"], ["Shanghai", "Shanghai"]]}}, {"id": "9264678a-c091-4866-90da-c52f7adbb539", "type": "entities_overlap", "source": "223bc919-db22-4f0e-bfbf-9f6508930ef2", "target": "********-fd84-453a-a35e-08efcfc73dd9", "bidirectional": false, "properties": {"entities_overlap_score": 0.0875, "overlapped_items": [["<PERSON><PERSON>", "<PERSON><PERSON>"], ["Tesla", "Tesla"], ["SpaceX", "SpaceX"], ["Europe", "Europe"], ["Asia", "Asia"], ["Berlin", "Berlin"], ["Shanghai", "Shanghai"]]}}, {"id": "1be6a163-bddb-4728-993e-d09823c1bc93", "type": "entities_overlap", "source": "a71eb9d5-d814-40ab-9f1e-9919b5ab9a93", "target": "********-fd84-453a-a35e-08efcfc73dd9", "bidirectional": false, "properties": {"entities_overlap_score": 0.09722222222222222, "overlapped_items": [["<PERSON><PERSON>", "<PERSON><PERSON>"], ["Tesla", "Tesla"], ["SpaceX", "SpaceX"], ["Europe", "Europe"], ["Asia", "Asia"], ["Berlin", "Berlin"], ["Shanghai", "Shanghai"]]}}, {"id": "78d8bc3d-b677-4c85-95f1-8e3ce3a84649", "type": "entities_overlap", "source": "f06c35f4-a311-4720-bb05-71268d20ef2c", "target": "2ebbbbfd-1be8-44c1-a7a7-b97e28e954e2", "bidirectional": false, "properties": {"entities_overlap_score": 0.125, "overlapped_items": [["TMRGs", "TMRG"], ["Team Member Resource Groups", "Team Member Resource Group"]]}}, {"id": "3113cc8d-ab95-447e-9096-78f9524bdabe", "type": "entities_overlap", "source": "f06c35f4-a311-4720-bb05-71268d20ef2c", "target": "58094ce6-70c1-425e-bd31-a3924ba65aa1", "bidirectional": false, "properties": {"entities_overlap_score": 0.020833333333333332, "overlapped_items": [["TMRGs", "TMRG"]]}}, {"id": "8b89a247-3e88-4584-bc87-5d2c1ac38eec", "type": "entities_overlap", "source": "f06c35f4-a311-4720-bb05-71268d20ef2c", "target": "6593e690-298f-4d7b-a8d9-94a09bff2ca4", "bidirectional": false, "properties": {"entities_overlap_score": 0.041666666666666664, "overlapped_items": [["Diversity", "Diversity"]]}}, {"id": "cd559d0f-5a12-4435-b928-2d149710491f", "type": "entities_overlap", "source": "2ebbbbfd-1be8-44c1-a7a7-b97e28e954e2", "target": "58094ce6-70c1-425e-bd31-a3924ba65aa1", "bidirectional": false, "properties": {"entities_overlap_score": 0.08333333333333333, "overlapped_items": [["TMRG", "TMRG"]]}}, {"id": "f8ac692f-6550-4f18-98a6-ad9a5842a7ac", "type": "entities_overlap", "source": "725a4936-bc3a-47cf-a189-ea2d6d91b3c6", "target": "bb993c58-9e3a-4a41-baef-ead3b7d7636f", "bidirectional": false, "properties": {"entities_overlap_score": 0.037037037037037035, "overlapped_items": [["Senior Leader", "Senior Leaders"], ["Sponsorship Program", "Sponsorship Program"]]}}, {"id": "480a7728-42be-4d36-b43e-30f8dc75881e", "type": "entities_overlap", "source": "725a4936-bc3a-47cf-a189-ea2d6d91b3c6", "target": "0690fc16-c737-4967-95f1-7378c3aaea38", "bidirectional": false, "properties": {"entities_overlap_score": 0.022222222222222223, "overlapped_items": [["sponsee", "Sponsee"]]}}]}