---
title: "Sales Sponsorship Pilot Program"
description: "A page for the Sales Sponsorship Pilot Program"
---

In 2023 we conducted a pilot program to address an imbalance of Black Team Members at job grade 9+ within Sales and lack of representation in sales leadership positions. The pilot program was designed to create pathways for Black Team Members to progress effectively at GitLab. We have since extended this program to promote pathways for all Sales team members to progress at GitLab.

## Mentorship vs Sponsorship

[Harvard Business Review](https://hbr.org/2019/08/a-lack-of-sponsorship-is-keeping-women-from-advancing-into-leadership) defines the difference between sponsors versus mentors: “While a mentor is someone who has knowledge and will share it with you, a sponsor is a person who has power and will use it for you.”

**Mentoring** is defined in our [Mentoring at GitLab handbook page as:](/handbook/people-group/learning-and-development/mentor#what-is-mentoring) Mentor relationships are an opportunity for individuals to learn from someone's personal experience, background, and perspective. These relationships build trust on a team, provide safe space to make mistakes, and encourage both personal and professional development. Mentorship is an opportunity for both the mentor and mentee to develop their leadership and communication skills. Mentorship should be led by the mentee, similar to how 1:1's at GitLab are driven by direct reports.

**Sponsorship** is defined using influence and/or power to support the growth and development of a team member. A sponsor is typically a senior leader with significant influence and responsibilities. A sponsor will then use this influence to but not limited to; advocating for career advancement opportunities, provide opportunities to upskills via active projects and provide visibility to other senior leaders and the business of the sponsees potential.

## Role of a Sponsor at GitLab

**A Sponsor is someone who has power and influence at GitLab and will use that power to advocate, elevate and impact a team members opportunities and career progression at GitLab.**

**A sponsor at GitLab is:**

- A Senior Leader at a minimum [job grade 10+]({{< ref "compensation-calculator#job-grades" >}}) and is not the sponsees direct manager.
    - The senior leader should be a People Manager or Manager of Managers
- Must have been at GitLab for 6+ months
- Be able to commit to a 9 month sponsorship program which will include
    - Sponsor Training
    - Kick-off calls
    - Bi-weekly 1-1 with Sponsee
    - Be able to fully commit and be held accountable for their role within the Sponsorship Program

**As a Sponsor you should be:**

- Be open and honest
- Listen and Learn
- Lean into discomfort in areas not familiar to you (e.g. cultural differences)
- Take risks with the relationship
- Advocate for your sponsee with intent
- Give feedback as appropriate
- Be intentional in building the relationship

**A sponsor will:**

- Use their influence and power to advocate for the sponsee potential for career advancement
- Use their influence to increase the visibility of the sponsee amongst other Senior Leaders at GitLab
- Use their influence to increase the visibility of the sponsee at GitLab
- Provide opportunities and space for risk-taking and growth
- Provide feedback on growth and skill development

**The sponsorship relationship may not provide all the above but the sponsor should be willing and able to provide some of the opportunities above to the sponsor.**

## Role of a Sponsee

**A Sponsee at GitLab will be (Pilot Program Only):**

- Be performing in your role in accordance with your managers expectations, which take into account performance exercises such as 9 box and not on a Performance Improvement Plan
- Be a part of the underrepresented group the sponsorship program is targeted at.
- Be able to commit to 9 months within the program
- Willing to commit to the overall expectations of the program as outlined throughout the handbook page

**As a Sponsee you should be:**

- Be able to lead the relationship with your sponsor
- Be able Take risks with the relationship—trust is a vital part of the relationship
- Be able to do the work required based on feedback received and requirements for success
- Be able to request and give feedback as appropriate
- Be able to take personal responsibility for your career and be empowered to ask for what you need

**A Sponsee will:**

- Maintain or exceed their performance record
- Maintain trust & confidentiality in the relationship
- Assist the sponsor in insights to the business and challenging perceived norms
- Be an ambassador for our CREDIT Values
- Have aspirational goals for career development; within leadership or as a senior IC
- Have a growth mindset and will be able to continually learn
- Have the ability to take on special projects which will impact their growth
- Makes themselves available for opportunities and stretch assignments
- Consistently performing at or above the performance bar

### Role of Sponsees Manager

- The manager will support the team member in ensuring that have a Individual Growth Plan (IGP)
    - As well reassess the IGP at regular intervals during the sponsorship program
- The manager will regularly check in on the progress of the Sponsorship Program with their report
- The manager will regularly report on the success of the program to the leadership advocates of the program
- Participate in a manager of sponsees session

## Sponsorship Program

The program will last 6 months and the Sponsor & Sponsee will be the same person throughout the program.

**The program will consist of:**

- An Individual Growth Plan is created in conjunction with your direct manager. The sponsor will add further suggestions throughout the program
- 1-1 conversation in a cadence agreed upon by the participants.
    - We suggest a minimum of bi-weekly.
    - Special projects & Sponsorship activities may mean cadence changes
- Quarterly All-Hands
    - This will reinforce the ideals of the sponsorship program and ensure that the relationship is progressing through the phases.
- Individual Quarterly Feedback with DIB Team
- End of Program session with Sponsor
- Complete Next Step Documentation with Sponsor & Direct Manager to ensure progression continues

**Sponsor Sponsee Matching:**

- A sponsor should be at least two job grades higher than the sponsee
- A sponsor and sponsee should be in a timezone that is practical to have 1-1s. Special projects etc can follow asynchronous principles
- If possible Sponsor should align with the career development plan of the sponsee

## What does a successful sponsorship look like?

**Build**

Take the time to build a solid relationship with each other. This will be particularly important if you have no previous direct working relationship and can often take some time to cultivate. It is very important to build the relationship first before moving into authentic sponsorship.

Suggested Actions:

- Commit to regular 1-1s
- Understand the sponsees career development plan
- Set goals and expectations early

**Develop**

You have taken the time to build a relationship with each other, the next step is to develop that relationship by becoming action & capability focussed. In this step the sponsor will help guide the sponsee on areas of improvement in skills & capabilities. The sponsee is responsible for acting on feedback and being intentional about displaying these improvements to the sponsor.

Suggested Actions:

- Find and seek opportunities for the sponsor to observe the sponsees improvement areas
- Sponsee invites Sponsor to a team meeting where they are presenting
- Sponsor invites sponsee to a working group

**Commit**

This is where both parties agree to move forward with the next step, which is sponsorship and advocating for the sponsee. This can take many forms, such as; a formal discussion, the sponsor outlining actions to the sponsee or at the sponsees request. This is an opportunity to provide feedback, any uncertainties and to reestablish career development goals.

Suggested Actions:

- Participate in feedback session with sponsee

**Advocate**

Now that a commitment has been made and the sponsor is satisfied that the sponsee is ready for the next step. The sponsor actively and intentionally advocates for sponsees continued career development and advancement at GitLab.

## Goals & Benefits of Sponsorship Program

**Goals:**

The goal of this program is to provide team members from underrepresented groups opportunities to have more visibility at GitLab. We are starting with the Black team member population in sales as they are very underrepresented in all areas of leadership. The goal is that programs like this will help redress the imbalance and ensure we are moving towards a more diverse, equitable and inclusive workplace.

**Benefits:**

Sponsor:

- Exposure to a diverse set of team members at GitLab, increasing their ability to lead diverse teams
- Exposure to new ideas and be challenged on the status quo
- Intentional talent management  & succession planning
- Better understanding of the challenges of team members from underrepresented groups

Sponsee:

- Increased exposure to GitLab and visibility to senior leaders at GitLab
- Access to feedback from a senior leader
- Ability to develop skills and capabilities
- Increased control over your career development plan

GitLab:

- A more diverse team at leadership team
- Increased retention of team members
- Attraction of underrepresented groups to GitLab
- Team members more motivated to maintain performance levels

## Measurables

- Start, Mid and End of the program - Satisfaction score from both Sponsor and Sponsee
- Career Advancement Rate within twelve months of the program

## Pilot Project Plan

**Phase 1:**

- Develop training materials for Sponsors & Sponsees => `0%`
- Develop program materials => `0%`
- Sponsorship 1-1 guide => `0%`
- Next Steps Doc - for End of Program => `0%`
- Identify first cohort of sponsees amongst Team Members in Sales => `0%`
- What criteria they have for a sponsor => `0%`
- Identify first cohort of sponsors with the Sales Org => `0%`
- What expertise they are willing to provide and opportunities they can provide => `0%`
- Matchmaking of Sponsors & Sponsee => `0%`

Complete => => `0%`

**Phase 2:**

- Conduct Kick-Off call to present the program => `0%`
- Conduct Sponsorship Training => `0%`
- Release Sponsorship Matches => `0%`
- Conduct Pre-Start AMA => `0%`

**Phase 3:**

- Begin the Sponsorship Program => `0%`
- Quarterly check in call - Group => `0%`
- Quarterly feedback individual check in => `0%`
- Next Steps Doc completion => `0%`
- End Program => `0%`

**Phase 4:**

- Retrospective on Program Success/Improvements => `0%`
- Final feedback from Participants => `0%`
- Provide iteration recommendations => `0%`
