"""
简单的 RAGAS 集成测试
"""

import sys
import os
import traceback

# 添加父目录到路径以便导入 jina_embeddings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试导入"""
    print("📦 测试导入...")
    
    try:
        from jina_embeddings import create_jina_embeddings
        print("✅ jina_embeddings 导入成功")
    except Exception as e:
        print(f"❌ jina_embeddings 导入失败: {e}")
        return False
    
    try:
        from ragas import SingleTurnSample
        print("✅ SingleTurnSample 导入成功")
    except Exception as e:
        print(f"❌ SingleTurnSample 导入失败: {e}")
        return False
    
    try:
        from ragas.metrics import SemanticSimilarity
        print("✅ SemanticSimilarity 导入成功")
    except Exception as e:
        print(f"❌ SemanticSimilarity 导入失败: {e}")
        traceback.print_exc()
        return False
    
    return True


def test_basic_functionality():
    """测试基本功能"""
    print("\n🧪 测试基本功能...")
    
    try:
        from jina_embeddings import create_jina_embeddings
        from ragas import SingleTurnSample
        
        # 创建嵌入实例
        api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
        embeddings = create_jina_embeddings(
            api_key=api_key,
            task="retrieval.query"
        )
        print("✅ 嵌入实例创建成功")
        
        # 测试嵌入
        test_text = "这是一个测试"
        vector = embeddings.embed_query(test_text)
        print(f"✅ 嵌入测试成功，维度: {len(vector)}")
        
        # 创建样本
        sample = SingleTurnSample(
            user_input="测试问题",
            response="测试回答",
            reference="参考答案"
        )
        print("✅ 样本创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        traceback.print_exc()
        return False


def test_semantic_similarity():
    """测试语义相似度"""
    print("\n📊 测试语义相似度...")
    
    try:
        from jina_embeddings import create_jina_embeddings
        from ragas import SingleTurnSample
        from ragas.metrics import SemanticSimilarity
        
        # 创建嵌入实例
        api_key = "jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR"
        embeddings = create_jina_embeddings(
            api_key=api_key,
            task="retrieval.query"
        )
        
        # 创建语义相似度指标
        metric = SemanticSimilarity(embeddings=embeddings)
        print("✅ 语义相似度指标创建成功")
        
        # 创建测试样本
        sample = SingleTurnSample(
            user_input="什么是人工智能？",
            response="人工智能是计算机科学的一个分支。",
            reference="AI是计算机科学领域。"
        )
        
        # 计算相似度
        score = metric.single_turn_score(sample)
        print(f"✅ 语义相似度计算成功: {score:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 语义相似度测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 简单 RAGAS 集成测试")
    print("=" * 40)
    
    # 测试导入
    import_success = test_imports()
    if not import_success:
        print("❌ 导入测试失败，退出")
        return
    
    # 测试基本功能
    basic_success = test_basic_functionality()
    if not basic_success:
        print("❌ 基本功能测试失败，退出")
        return
    
    # 测试语义相似度
    similarity_success = test_semantic_similarity()
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    print(f"   导入测试: {'✅ 通过' if import_success else '❌ 失败'}")
    print(f"   基本功能: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"   语义相似度: {'✅ 通过' if similarity_success else '❌ 失败'}")
    
    if all([import_success, basic_success, similarity_success]):
        print("\n🎉 所有测试都通过了！")
        print("💡 Jina AI 嵌入已成功集成到 RAGAS 中！")
    else:
        print("\n⚠️  部分测试失败")


if __name__ == "__main__":
    main()
