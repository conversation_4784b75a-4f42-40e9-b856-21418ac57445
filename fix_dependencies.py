#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG系统依赖修复脚本
Fix dependencies for RAG system
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并处理错误"""
    print(f"\n正在执行: {description}")
    print(f"命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 成功: {description}")
            if result.stdout:
                print(f"输出: {result.stdout.strip()}")
        else:
            print(f"❌ 失败: {description}")
            print(f"错误: {result.stderr.strip()}")
        return result.returncode == 0
    except Exception as e:
        print(f"❌ 异常: {e}")
        return False

def fix_libmagic():
    """修复libmagic问题"""
    print("=" * 50)
    print("修复libmagic问题")
    print("=" * 50)
    
    # 检测操作系统
    if os.name == 'nt':  # Windows
        print("检测到Windows系统")
        commands = [
            ("pip install python-magic-bin", "安装Windows版本的python-magic"),
        ]
    else:  # Linux/Mac
        print("检测到Linux/Mac系统")
        commands = [
            ("pip install python-magic", "安装python-magic"),
        ]
    
    for command, description in commands:
        run_command(command, description)

def install_optional_dependencies():
    """安装可选依赖"""
    print("\n" + "=" * 50)
    print("安装可选依赖")
    print("=" * 50)
    
    optional_packages = [
        ("pip install --upgrade certifi", "更新certifi"),
        ("pip install --upgrade requests", "更新requests"),
        ("pip install markdown", "安装markdown处理器"),
    ]
    
    for command, description in optional_packages:
        run_command(command, description)

def test_imports():
    """测试关键模块导入"""
    print("\n" + "=" * 50)
    print("测试模块导入")
    print("=" * 50)
    
    test_modules = [
        "langchain_openai",
        "jina_embeddings", 
        "numpy",
        "ragas",
    ]
    
    for module in test_modules:
        try:
            __import__(module)
            print(f"✅ {module} - 导入成功")
        except ImportError as e:
            print(f"❌ {module} - 导入失败: {e}")
        except Exception as e:
            print(f"⚠️ {module} - 其他错误: {e}")

def create_safe_document_loader():
    """创建安全的文档加载器"""
    print("\n" + "=" * 50)
    print("创建安全文档加载器")
    print("=" * 50)
    
    loader_code = '''
import os
import glob
from pathlib import Path

def safe_load_markdown_files(directory_path, max_files=10):
    """
    安全地加载markdown文件，过滤掉有问题的文件
    Safely load markdown files, filtering out problematic files
    """
    documents = []
    failed_files = []
    
    if not os.path.exists(directory_path):
        print(f"目录不存在: {directory_path}")
        return documents, failed_files
    
    # 获取所有markdown文件
    md_files = glob.glob(os.path.join(directory_path, "*.md"))
    
    print(f"找到 {len(md_files)} 个markdown文件")
    
    for file_path in md_files[:max_files]:
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
                # 过滤掉包含模板语法的文件
                if '{{' in content or '}}' in content:
                    print(f"跳过模板文件: {os.path.basename(file_path)}")
                    continue
                    
                # 过滤掉太短的文件
                if len(content.strip()) < 50:
                    print(f"跳过内容过短的文件: {os.path.basename(file_path)}")
                    continue
                
                # 移除markdown标记，只保留文本内容
                clean_content = content.replace('---', '').replace('#', '').strip()
                clean_content = '\\n'.join([line for line in clean_content.split('\\n') if line.strip()])
                
                if clean_content and len(clean_content) > 50:
                    documents.append(clean_content)
                    print(f"成功加载: {os.path.basename(file_path)}")
                    
        except Exception as e:
            failed_files.append((file_path, str(e)))
            print(f"加载失败: {os.path.basename(file_path)} - {e}")
    
    print(f"\\n成功加载 {len(documents)} 个文档")
    if failed_files:
        print(f"失败 {len(failed_files)} 个文件")
    
    return documents, failed_files

# 测试函数
if __name__ == "__main__":
    # 测试加载Sample_Docs_Markdown目录
    if os.path.exists('Sample_Docs_Markdown'):
        docs, failed = safe_load_markdown_files('Sample_Docs_Markdown')
        print(f"\\n加载结果: {len(docs)} 个文档成功, {len(failed)} 个失败")
        
        if docs:
            print("\\n文档预览:")
            for i, doc in enumerate(docs[:3], 1):
                print(f"{i}. {doc[:100]}...")
    else:
        print("Sample_Docs_Markdown 目录不存在")
'''
    
    try:
        with open('safe_document_loader.py', 'w', encoding='utf-8') as f:
            f.write(loader_code)
        print("✅ 创建 safe_document_loader.py 成功")
        
        # 测试运行
        result = subprocess.run([sys.executable, 'safe_document_loader.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 文档加载器测试成功")
            print(result.stdout)
        else:
            print("⚠️ 文档加载器测试有警告")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 创建文档加载器失败: {e}")

def main():
    """主函数"""
    print("RAG系统依赖修复脚本")
    print("=" * 50)
    
    # 1. 修复libmagic
    fix_libmagic()
    
    # 2. 安装可选依赖
    install_optional_dependencies()
    
    # 3. 测试导入
    test_imports()
    
    # 4. 创建安全文档加载器
    create_safe_document_loader()
    
    print("\n" + "=" * 50)
    print("修复完成!")
    print("=" * 50)
    print("\n建议:")
    print("1. 重启Jupyter notebook")
    print("2. 使用 safe_document_loader.py 来安全加载文档")
    print("3. libmagic警告可以忽略，不影响核心功能")

if __name__ == "__main__":
    main()
