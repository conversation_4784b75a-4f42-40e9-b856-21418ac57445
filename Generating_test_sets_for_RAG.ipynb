# 导入 LangChain 的文档加载器
from langchain_community.document_loaders import DirectoryLoader

# 指定包含 Markdown 文档的目录路径
path = "Sample_Docs_Markdown/"

# 创建目录加载器，使用 glob 模式匹配所有 .md 文件（包括子目录）
# "**/*.md" 表示递归搜索所有子目录中的 .md 文件
loader = DirectoryLoader(path, glob="**/*.md")

# 加载所有文档，返回 Document 对象列表
# 每个 Document 包含 page_content（文档内容）和 metadata（元数据）
docs = loader.load()

print(f"成功加载 {len(docs)} 个文档")

# 导入 LangChain 的 OpenAI 聊天模型
from langchain_openai import ChatOpenAI

# 配置大语言模型
# 使用阿里云通义千问模型，通过 OpenAI 兼容接口调用
llm = ChatOpenAI(
    openai_api_base="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 阿里云 API 端点
    openai_api_key="sk-282f3112bd714d6e85540da173b5517c",  # API 密钥（实际使用时请替换为您的密钥）
    model="qwen-plus-2025-01-25"  # 模型名称
)

print("大语言模型配置完成")

# 以下是使用 OpenAI 模型的示例配置（已注释）
# from ragas.llms import LangchainLLMWrapper
# from ragas.embeddings import LangchainEmbeddingsWrapper
# from langchain_openai import ChatOpenAI
# from langchain_openai import OpenAIEmbeddings
# generator_llm = LangchainLLMWrapper(ChatOpenAI(model="gpt-4o"))
# generator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings())

# 导入 Ragas 的模型包装器
from ragas.llms import LangchainLLMWrapper
from ragas.embeddings import LangchainEmbeddingsWrapper
from jina_embeddings import create_jina_embeddings

# 将 LangChain LLM 包装为 Ragas 兼容格式
# 用于生成测试问题和答案
generator_llm = LangchainLLMWrapper(llm)

# 配置嵌入模型
# 使用 Jina AI 的嵌入模型来计算文档和查询的向量表示
# 嵌入模型用于文档相似性计算和检索
generator_embeddings = LangchainEmbeddingsWrapper(
    create_jina_embeddings(
        api_key='jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'  # Jina AI API 密钥
    )
)

print("模型包装器配置完成")

# 导入 Ragas 测试集生成器
from ragas.testset import TestsetGenerator

# 创建测试集生成器实例
# 需要提供大语言模型和嵌入模型
generator = TestsetGenerator(
    llm=generator_llm,                    # 用于生成问题和答案的大语言模型
    embedding_model=generator_embeddings   # 用于文档嵌入和相似性计算的嵌入模型
)

# 从 LangChain 文档生成测试集
# 这个方法会自动执行以下步骤：
# 1. 提取文档标题和结构信息
# 2. 生成文档摘要
# 3. 提取主题和命名实体
# 4. 计算文档嵌入和相似性
# 5. 生成用户画像（personas）
# 6. 生成测试场景
# 7. 生成最终的问答样本
dataset = generator.generate_with_langchain_docs(
    docs,              # 输入文档列表
    testset_size=10    # 生成的测试样本数量
)

print(f"成功生成包含 {len(dataset)} 个样本的测试集")

# 将测试集转换为 Pandas DataFrame 格式进行查看
# 测试集包含以下列：
# - user_input: 用户问题/查询
# - reference_contexts: 相关的参考上下文（从文档中提取）
# - reference: 标准答案/参考答案
# - synthesizer_name: 生成器类型（单跳或多跳查询合成器）

df = dataset.to_pandas()
print(f"测试集包含 {len(df)} 个样本")
print(f"其中单跳查询: {len(df[df['synthesizer_name'] == 'single_hop_specifc_query_synthesizer'])} 个")
print(f"多跳查询: {len(df[df['synthesizer_name'] == 'multi_hop_specific_query_synthesizer'])} 个")

# 显示测试集内容
df

# 导入知识图谱相关类
from ragas.testset.graph import KnowledgeGraph

# 创建空的知识图谱实例
# 知识图谱将用于存储文档节点和它们之间的关系
kg = KnowledgeGraph()

print("知识图谱初始化完成")

# 导入节点相关类
from ragas.testset.graph import Node, NodeType

# 遍历所有加载的文档，为每个文档创建一个图节点
for doc in docs:
    # 创建文档类型的节点
    node = Node(
        type=NodeType.DOCUMENT,  # 节点类型：文档
        properties={
            "page_content": doc.page_content,  # 文档内容
            "document_metadata": doc.metadata   # 文档元数据（如文件路径、标题等）
        }
    )
    
    # 将节点添加到知识图谱中
    kg.nodes.append(node)

print(f"成功添加 {len(kg.nodes)} 个文档节点到知识图谱")

# 导入转换相关模块
from ragas.testset.transforms import default_transforms, apply_transforms

# 配置转换所需的模型
# 使用与测试集生成相同的 LLM 和嵌入模型以保持一致性
transformer_llm = generator_llm      # 用于文本生成任务（如摘要生成）
embedding_model = generator_embeddings  # 用于向量嵌入计算

# 创建默认的转换管道
# 这个管道包含了多个转换步骤，用于丰富知识图谱的信息
trans = default_transforms(
    documents=docs,                    # 原始文档
    llm=transformer_llm,              # 大语言模型
    embedding_model=embedding_model    # 嵌入模型
)

# 将转换应用到知识图谱
# 这个过程会：
# 1. 提取文档标题和结构
# 2. 生成文档摘要
# 3. 提取主题和命名实体
# 4. 计算文档嵌入
# 5. 建立文档间的相似性关系
apply_transforms(kg, trans)

print("知识图谱转换完成")

# 将知识图谱保存到 JSON 文件
# 这样可以避免重复构建，提高效率
kg.save("knowledge_graph.json")
print("知识图谱已保存到 knowledge_graph.json")

# 从文件加载知识图谱
# 演示如何重新加载已保存的知识图谱
loaded_kg = KnowledgeGraph.load("knowledge_graph.json")
print(f"成功加载知识图谱：{loaded_kg.nodes} 个节点，{loaded_kg.relationships} 个关系")

# 显示知识图谱的基本信息
loaded_kg

# 导入测试集生成器（如果之前没有导入）
from ragas.testset import TestsetGenerator

# 创建基于知识图谱的测试集生成器
# 这个生成器能够利用知识图谱中的结构信息和关系
generator = TestsetGenerator(
    llm=generator_llm,              # 大语言模型
    embedding_model=embedding_model, # 嵌入模型
    knowledge_graph=loaded_kg       # 知识图谱（关键差异）
)

print("基于知识图谱的测试集生成器创建完成")

# 导入查询分布配置
from ragas.testset.synthesizers import default_query_distribution

# 创建默认的查询分布配置
# 这定义了不同类型查询的生成比例：
# - 简单查询：直接从单个文档中可以回答的问题
# - 推理查询：需要一定推理能力的问题
# - 多跳查询：需要跨多个文档推理的复杂问题
# - 条件查询：包含条件判断的问题
query_distribution = default_query_distribution(generator_llm)

print("查询分布配置完成")
print("支持的查询类型：简单查询、推理查询、多跳查询、条件查询")

# 尝试使用知识图谱生成测试集
# 这种方法能够生成更复杂的多跳推理问题
try:
    testset = generator.generate(
        testset_size=10,                    # 生成 10 个测试样本
        query_distribution=query_distribution  # 使用配置的查询分布
    )
    
    # 显示生成的测试集
    print("基于知识图谱的测试集生成成功！")
    testset_df = testset.to_pandas()
    print(f"生成了 {len(testset_df)} 个测试样本")
    display(testset_df)
    
except ValueError as e:
    print(f"生成失败：{e}")
    print("\n可能的原因：")
    print("1. 文档数量较少，无法形成有效的聚类")
    print("2. 文档间相似性不足，缺乏明显的关系")
    print("3. 知识图谱的关系阈值设置过高")
    print("\n建议解决方案：")
    print("1. 增加更多相关文档")
    print("2. 调整关系条件参数")
    print("3. 使用 generate_with_langchain_docs() 方法作为替代")