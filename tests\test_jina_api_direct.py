"""
直接测试 Jina AI API 调用
使用官方示例格式
"""

import requests
import json


def test_direct_api_call():
    """直接测试 API 调用"""
    print("🧪 直接测试 Jina AI API...")
    
    url = 'https://api.jina.ai/v1/embeddings'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'
    }
    data = {
        "model": "jina-embeddings-v3",
        "task": "retrieval.query",
        "input": [
            "人工智能正在改变世界",
            "机器学习是AI的重要分支",
            "深度学习需要大量数据"
        ]
    }
    
    try:
        print("📝 发送 API 请求...")
        response = requests.post(url, headers=headers, json=data)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API 调用成功！")
            print(f"📈 返回数据结构: {list(result.keys())}")
            
            if 'data' in result:
                embeddings = result['data']
                print(f"🎯 获得 {len(embeddings)} 个嵌入向量")
                if embeddings:
                    print(f"📏 向量维度: {len(embeddings[0]['embedding'])}")
                    print(f"🔢 第一个向量前5个值: {embeddings[0]['embedding'][:5]}")
            
            return True
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_different_tasks():
    """测试不同的任务类型"""
    print("\n🔄 测试不同的任务类型...")
    
    url = 'https://api.jina.ai/v1/embeddings'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer jina_03b029f4a86943afb00a631f821c87f8IG_W2P6VczaabvU7iiMBIte_G0zR'
    }
    
    tasks = [
        "retrieval.query",
        "retrieval.passage", 
        "text-matching",
        "classification"
    ]
    
    test_text = ["这是一个测试文本"]
    
    for task in tasks:
        try:
            data = {
                "model": "jina-embeddings-v3",
                "task": task,
                "input": test_text
            }
            
            print(f"📝 测试任务类型: {task}")
            response = requests.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    vector_dim = len(result['data'][0]['embedding'])
                    print(f"   ✅ 成功，向量维度: {vector_dim}")
                else:
                    print(f"   ⚠️  响应格式异常")
            else:
                print(f"   ❌ 失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")


def main():
    """主函数"""
    print("🚀 Jina AI API 直接测试")
    print("=" * 40)
    
    # 直接 API 测试
    direct_success = test_direct_api_call()
    
    # 不同任务类型测试
    if direct_success:
        test_different_tasks()
    
    print("\n" + "=" * 40)
    if direct_success:
        print("🎉 直接 API 测试成功！")
        print("💡 现在可以测试我们的自定义嵌入实现了")
    else:
        print("⚠️  直接 API 测试失败，请检查 API 密钥和网络")


if __name__ == "__main__":
    main()
